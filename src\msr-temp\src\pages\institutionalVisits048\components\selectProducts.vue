<template>
  <!-- 产品信息选择 -->
  <view class="selectProducts">
    <TopSearchDefault
        v-if="appCode !== 'athena_gen'"
        fixed="fixed"
        @top-search="search"
        placeholder="请输入产品名称"
    />
    <view class="pop-search" v-else>
      <nut-popover v-model:visible="show1" :list="state.productClassification" location="bottom" @choose="choose" style="width: 124px" :offset="[80, 10]">
        <template #reference>
          <view style="background: #fff; font-size: 14px; display: flex; align-items: center; padding-left: 16px; gap: 4px; min-width: 68px">
            <view v-if="state.params.classification && state.params.classification?.length">{{ state.params?.classification?.[0].split('（')?.[0] }}</view>
            <view v-else>产品分类</view>
            <img :src="vectorImg" style="width: 6px; height: 4px" />
          </view>
        </template>
      </nut-popover>
      <TopSearch
          fixed="fixed"
          @top-search="search"
          placeholder="请输入产品名称"
          :style="{paddingBottom: 0}"
      />
    </view>

    <view class="contentMargin27">
      <view
        class="pro-content"
        v-for="item in state.itutionProductList"
        :key="item.productCode"
      >
        <view
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: #fff;
          "
          @click="clickVisit(item.productCode)"
        >
          <view class="productName">{{ item.productName }}</view>
          <img
            :src="!item.infoIconFlag ? arrowDown : arrowUp"
            alt=""
            v-if="item?.productInfoList.length != 0"
            style="width: 13px; height: 13px; padding-left: 10px"
          />
        </view>
        <view
          v-if="item.infoIconFlag && item?.productInfoList.length != 0"
          class="pro-item"
        >
          <nut-checkbox-group
            ref="group"
            v-model="item.checkboxed"
            @change="change2"
          >
            <nut-checkbox
              v-for="j in item.productInfoList"
              :key="j.infoId"
              :label="j.infoId"
              text-position="left"
            >
              <template #icon>
                <img :src="icon" alt="" class="radioIcon" />
              </template>
              <template #checkedIcon>
                <img :src="checkedIcon" alt="" class="radioIcon" />
              </template>
              {{ j.info }}
            </nut-checkbox>
          </nut-checkbox-group>

          <view> </view>
        </view>
      </view>
    </view>

    <FooterButton
      :isTextType="true"
      :text="state.text"
      @click-button="clickButton"
    />
  </view>
</template>

<script setup>
import Taro, { useRouter } from "@tarojs/taro";
import {
  defineEmits,
  defineProps,
  nextTick,
  onMounted,
  reactive,
  ref,
} from "vue";
import { institutionProducts, dicProCateApi } from "../../../api/institutionalVisitsApi.js";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import arrowDown from "../../../images/arrow-down.png";
import arrowUp from "../../../images/up.png";
import icon from "../../../images/radio-icon1.png";
import checkedIcon from "../../../images/radio-icon2.png";
import TopSearch from "./topSearch2.vue";
import TopSearchDefault from "../../../pages/components/topSearch/topSearch.vue";

import vectorImg from '../../../images/vector.png';

import GlobalNutCheckbox from "../../../pages/components/globalNutCheckbox/globalNutCheckbox.vue";
const router = useRouter();
const query = router.params;
const initHrInfo = Taro.getStorageSync("initHrInfo");

import { appCode } from "@/utils/content";

const props = defineProps({
  institutionObject: {
    type: Object,
    default: () => {
      return {};
    },
  },
  customerObject: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const emit = defineEmits(["button-product"]);
const state = reactive({
  leftTetx: "产品信息选择",
  text: "确认",
  checkedResult: [],
  productClassification: [],
  params: {
    insCode: "",
    jurCodeList: initHrInfo.jurCodeList,
    selfJurCodeList: initHrInfo.selfJurCodeList,
    subJurCodeList: initHrInfo.subJurCodeList,
    productName: "",
  },
  itutionProductList: [],
});
const group = ref(null);

const show1 = ref(false)
const list = ref([])
const choose = async (item, index) => {
  console.log(item, index)
  state.params.classification = item.remark === state.params.classification?.[0]?.split('（')?.[0] ? [] : [item.dictLabel];
  await getInstitutionProducts(state.params.productName)
}

const clickVisit = (code) => {
  state.itutionProductList.forEach((item) => {
    if (item.productCode === code) {
      item.infoIconFlag = !item.infoIconFlag;
    }
  });
};
const search = (name) => getInstitutionProducts(name);
const getSelectedProductInfos = (institutionProductList) => {
  institutionProductList.forEach((product) => {
    const checkedInfoIds = new Set(product.checkboxed);
    const productInfoList = product.productInfoList;

    const filteredProductInfoList = productInfoList.filter((info) =>
      checkedInfoIds.has(info.infoId)
    );
    product.productInfoList = filteredProductInfoList;
  });
  return institutionProductList;
};

const clickButton = () => {
  const list = state.itutionProductList.filter(
    (item) => item.checkboxed.length
  );
  if (!list.length) {
    Taro.showToast({
      title: "请至少选择一个产品传递观念",
      icon: "none",
    });
    return;
  } else {
    state.itutionProductList = state.itutionProductList.filter(
      (item) => item.checkboxed.length
    );
    const selectePro = getSelectedProductInfos(state.itutionProductList);
    Taro.setStorage({ key: "selectePro", data: selectePro });
    Taro.navigateBack({
      delta: 1,
    });
  }
};

const getInstitutionProducts = async (name = "") => {
  state.params.productName = name;
  Taro.showLoading({
    mask: true,
    title: "搜索中",
  });
  const res = await institutionProducts(state.params).catch((err) =>
    Taro.hideLoading()
  );
  Taro.hideLoading();

  state.itutionProductList = res.data.map((item, index) => ({
    ...item,
    infoIconFlag: index == 0,
    checkboxed: [],
    productInfoList: item.productInfoList.map((j) => ({
      ...j,
      cusProdRespCode: "",
      hasRecommendedTreatment: "否",
      hasTherapeuticExperience: "否",
      treatmentType: "",
      promotionInfo: {},
    })),
  }));
  console.log("wssss", state.itutionProductList);
  const list = Taro.getStorageSync("setPro");
  if (list.length) {
    state.itutionProductList.forEach((z) => {
      list.forEach((item) => {
        z.productInfoList.forEach((a) => {
          item.productInfoList.forEach((b) => {
            if (a.infoId === b.infoId) {
              a.cusProdRespCode = b.cusProdRespCode;
              a.hasRecommendedTreatment = b.hasRecommendedTreatment || "否";
              a.hasTherapeuticExperience = b.hasTherapeuticExperience || "否";
              a.treatmentType = b.treatmentType || "";
              a.promotionInfo = b.promotionInfo || {};
            }
          });
        });
        if (z.productCode === item.productCode) {
          z.checkboxed = item.productInfoList.map((i) => i.infoId);
        }
      });
    });

    console.log("处理过的", state.itutionProductList);
    // Taro.removeStorageSync("setPro");
  }
};

const getDicProCate = async () => {
  const res = await dicProCateApi();
  state.productClassification = (res.rows || []).map(d => ({...d, name: d.remark}));
}

onMounted(() => {
  Taro.setNavigationBarTitle({ title: "选择产品" });
  state.params.insCode = query.insCode;
  getInstitutionProducts();
  getDicProCate()
});
</script>

<style lang="scss">
.nut-popover .nut-popover-content {
  width: 124px !important;
}
.search-box {
}
.contentMargin27 {
  margin-top: 30px;
}
.productName {
  //styleName: 16;
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 400;
  // line-height: 22.4px;
  // text-align: left;
}
.selectProducts {
  padding-bottom: 75px;
  .pop-search {
    display: flex;
    background: #fff;
    align-items: center;
    padding-bottom: 2px;
    position: fixed;
    top: 0
  }
  .pro-content {
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.04);
    margin-bottom: 1px;
    .pro-item {
      padding: 7px 24px;
      background: #f5f5f5;
    }
    .nut-checkbox--reverse .nut-checkbox__label {
      color: #869199;
      font-size: 12px;
    }
  }
  .radioIcon {
    width: 20px;
    height: 20px;
    display: block;
  }
}
</style>
