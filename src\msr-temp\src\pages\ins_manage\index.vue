<!-- 机构列表页 -->
<template>
  <view class="arae">
    <SearchBar
      @seacrh-top="topInsSearch"
      @filters="insFilters"
      placeholder="请输入医疗机构名称或编码"
      v-show="state.currentTab === '0'"
      :active="state.activeFlag"
    />
    <SearchBar
      @seacrh-top="topCusSearch"
      @filters="cusFilters"
      placeholder="请输入机构名称/客户名称/科室名称"
      v-show="state.currentTab === '1'"
      :active="state.cusActiveFlag"
    />
    <SearchBar
      @seacrh-top="topApproveSearch"
      @filters="approveFilters"
      placeholder="请输入关键字搜索"
      v-show="state.currentTab === '3'"
      :filters="false"
    />
    <view>
      <nut-tabs
        v-model="state.currentTab"
        backgroud="#FFFFFF"
        @change="tabChange"
      >
        <nut-tab-pane title="我的机构" pane-key="0">
          <InsList
            ref="insListRef"
            :tab="state.currentTab"
            @change-pcdvalue="changePcdvalue"
            :appcodeIns="props.appcodeIns"
          />
        </nut-tab-pane>
        <nut-tab-pane title="机构审批" pane-key="3">
          <!-- <Approve ref="approveRef" /> -->
          <ApproveList
            ref="approveListRef"
            :tab="state.currentTab"
            @change-filter="changeApprove"
            processType="bpm_institution_apply"
          />
        </nut-tab-pane>
      </nut-tabs>
    </view>
    <view
      style="
        position: fixed;
        height: 56px;
        text-align: right;
        bottom: 80px;
        right: 16px;
      "
      @click="addClaimProduct"
      v-if="state.currentTab !== '3' && !isAis"
    >
      <view class="oval">
        <view style="margin-right: 8px"
          ><IconFont
            name="uploader"
            color="#fff"
            size="12px"
            style="display: block"
          ></IconFont
        ></view>

        <view style="color: #fff; font-size: 15px">新增</view>
      </view>
    </view>
    <custom-popup
      :show-popup-visible="state.showFooterPopupVisible"
      :closeable="false"
      @click-overlay="state.showFooterPopupVisible = false"
    >
      <institution-type
        source="area"
        @customerRouter="customerRouter"
        @clickTitle="state.showFooterPopupVisible = false"
      />
    </custom-popup>
  </view>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { ref, onMounted, reactive } from "vue";
import InsList from "./ins/instList.vue";
import CusList from "./customer/cusList.vue";
import SearchBar from "../../pages/components/searchBar";
import plus from "../../images/inst_plus.png";
import Approve from "./approve/index";
import CustomPopup from "../../pages/components/customPopup/index";
import InstitutionType from "../../pages/institutionalVisits/components/institutionType.vue";
import { jurCustomerUnique } from "../../api/area.js";
import ApproveList from "@/bpm-temp/src/pages/approve_list/index.vue";
import { IconFont } from "@nutui/icons-vue-taro";
import { isAis } from "../../utils/content.js";
const props = defineProps({
  appcodeIns: {
    type: String,
    default: "",
  },
  appcodeCust: {
    type: String,
    default: "",
  },
});
const router = useRouter();

const insListRef = ref(null);
const cusListRef = ref(null);
const approveRef = ref(null);
const visible1 = ref(false);
const approveListRef = ref(null);
const state = reactive({
  infoDefault: {},
  showFooterPopupVisible: false,

  currentTab: "0",
  activeFlag: false,
  cusActiveFlag: false,
});

// const clickBack = () => {
//   Taro.reLaunch({
//     url: "/pages/index/index",
//   });
// };
const onCancel = () => {
  console.log("event cancel");
  visible1.value = false;
};
const onOk = () => {
  console.log("event ok");
  visible1.value = false;
};
const noTitleClick = (bol, item) => {
  console.log(111111111111);
  visible1.value = bol;
};
const goAppraise = (item) => {
  console.log(item, "--item");
  state.infoDefault = item;
  state.showFooterPopupVisible = true;
};

const changePcdvalue = (value) => {
  if (value) {
    state.activeFlag = true;
  } else {
    state.activeFlag = false;
  }
};
const customerRouter = (itemUrl) => {
  Taro.setStorage({ key: "customerQuery", data: state.infoDefault });
  let url = `${itemUrl}?source=area`;
  state.showFooterPopupVisible = false;
  Taro.navigateTo({ url });
};

const changeFilter = (params) => {
  if (params.insName || params.insDept || params.teachTitle || params.job) {
    state.cusActiveFlag = true;
  } else {
    state.cusActiveFlag = false;
  }
};
const topInsSearch = (search) => {
  insListRef.value.topSearch(search);
};

const topCusSearch = (search) => {
  cusListRef.value.topSearch(search);
};
const insFilters = () => {
  insListRef.value.insFilters();
};
const cusFilters = (search) => {
  cusListRef.value.cusFilters(search);
};

const topApproveSearch = (search) => {
  // approveRef.value.topSearch(search);
  approveListRef.value.topSearch(search);
};
const tabChange = (tab = false) => {
  if (tab == 1) state.currentTab = "1";
  if (state.currentTab == "0") {
    insListRef.value.initInsList();
  }
  if (state.currentTab == "1") {
    cusListRef.value.initCusList();
  }
  if (state.currentTab == "3") {
    // approveRef.value.initList();
    approveListRef.value?.initList();
  }
};

const addClaimProduct = () => {
  let url = "";
  if (state.currentTab == "0") url = `/pages/ins_manage/search?type=ins_manage`;
  else url = `/pages/area/customer/search`;

  Taro.navigateTo({
    url,
  });
};

const getJurCustomerUnique = async () => {
  const res = await jurCustomerUnique();
  Taro.setStorage({ key: "jurCustomerUnique", data: res.msg });
};

useDidShow(() => {
  tabChange(router.params.currentTab);
});
onMounted(() => {
  Taro.setNavigationBarTitle({ title: "机构管理" });
  getJurCustomerUnique();
});
</script>

<style lang="scss">
input::placeholder {
  color: yellow;
}

.arae {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100vw;
  overflow: hidden;
  .searchBar {
    background: #fff;
  }
  .oval {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 82px;
    height: 41px;
    background: linear-gradient(144deg, #597dff 0%, #2551f2 100%);
    box-shadow: 0px 2px 4px 0px rgba(37, 81, 242, 0.45);
    border-radius: 100px 100px 100px 100px;
  }

  .nut-tabs__titles-item__line {
    background: blue;
  }

  .nut-tab-pane {
    background-color: transparent;
    padding: 18px 0px;
  }
  .nut-tab-pane {
    padding: 0 0;
  }

  .nut-searchbar__input-bar.nut-searchbar__input-bar_clear::placeholder {
    font-size: 12px;
  }
}
</style>
