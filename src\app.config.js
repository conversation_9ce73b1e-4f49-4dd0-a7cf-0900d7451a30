export default defineAppConfig({
  animation: {
    duration: 0,
    delay: 50,
  },
  pages: [
    "pages/login/index",
    "pages/ade/index",
    "pages/index/index",
    "pages/institutionalVisits/index",
    "pages/collaborativevisit/index",
    "pages/institutionalVisits/visiting",
    "pages/institutionalVisits/components/selectProducts",
    "pages/institutionalVisits/temporary",
    "pages/collaborativevisit/assistDetail",
    "pages/institutionalVisits/creacWaitplan/index",
    "pages/institutionalVisits/planDetail",
    "pages/institutionalVisits/sign/signIn",

    "pages/institutionalVisits048/index",
    "pages/institutionalVisits048/visiting",
    "pages/institutionalVisits048/components/selectProducts",
    "pages/institutionalVisits048/temporary",
    "pages/institutionalVisits048/creacWaitplan/index",
    "pages/institutionalVisits048/report/edit",
    "pages/institutionalVisits048/report/index",
    "pages/institutionalVisits048/planDetail",
    "pages/institutionalVisits048/sign/signIn",

    "pages/reportWeek/index",
    "pages/reportWeek/visiting",
    "pages/reportWeek/components/selectProducts",
    "pages/reportWeek/temporary",
    "pages/reportWeek/creacWaitplan/index",
    "pages/reportWeek/report/edit",
    "pages/reportWeek/report/index",
    "pages/reportWeek/planDetail",
    "pages/reportWeek/sign/signIn",

    "pages/collaborativevisit/components/appraise",
    "pages/area/index",
    "pages/area/customer/cusDeatil",
    "pages/area/customer/survey",
    "pages/area/ins/insDeatil",
    "pages/area/ins/insFilters",
    "pages/area/customer/cusFilters",
    "pages/area/components/editProduct",
    "pages/area/components/claimProduct",
    "pages/area/ins/search",
    "pages/area/customer/search",
    "pages/area/customer/addCus",
    "pages/area/ins/addIns",
    "pages/pharmacy/index",
    "pages/pharmacy/approve/approveFiltes",
    "pages/pharmacy/components/search",
    "pages/pharmacy/components/pharmacyDetail",
    "pages/pharmacy/components/addPharmacy",
    "pages/components/gdmap",
    "pages/association/index",
    "pages/association/detail/index",
    "pages/target/index",
    "pages/target/detail",
    "pages/insSign/index",
    "pages/insSign/visiting",
    "pages/insSign/sign/signIn",
    "pages/trajectory/index",
    "pages/trajectory/gdmap/index",
    "pages/insSignSkn/index",
    "pages/insSignSkn/visiting",
    "pages/insSignSkn/visitingInfo",
    "pages/insSignSkn/sign/signIn",
    "pages/speaker/index",
    "pages/speaker/components/search",
    "pages/speaker/components/speakerDetail",
    "pages/speaker/components/addSpeaker",
    "pages/ins_manage/index",
    "pages/customer_manage/index",
    "pages/ins_manage/search",
    "pages/ins_manage/detail",
    "pages/customer_manage/search",
    "pages/customer_manage/detail",
    "bpm-temp/src/pages/approve_detail/index",
  ],
  window: {
    backgroundTextStyle: "light",
    navigationBarBackgroundColor: "#fff",
    navigationBarTextStyle: "black",
    // navigationStyle: "custom",
  },
  permission: {
    "scope.userLocation": {
      desc: "获取地理位置信息的用途描述",
    },
  },
});
