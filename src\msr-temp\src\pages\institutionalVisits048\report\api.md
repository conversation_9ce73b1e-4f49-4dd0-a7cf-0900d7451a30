---
title: athena-cloud
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# athena-cloud

Base URLs:

# Authentication

# 金项链/标品/athena-tenant-ade/048客户上报

<a id="opIdaddOrUpdate"></a>

## POST 新增或更新上报

POST /ade/crmReport

新增或更新上报

> Body 请求参数

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[CrmReport](#schemacrmreport)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

<a id="opIdlistAll"></a>

## GET listAll

GET /ade/crmReport/list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":[{"createBy":0,"createTime":"2019-08-24T14:15:22Z","updateBy":0,"updateTime":"2019-08-24T14:15:22Z","params":{"property1":{},"property2":{}},"tenantId":"string","id":0,"insCode":"string","customerCode":"string","period":"string","num":"string","empCode":"string","deptCode":"string","postCode":"string","reportTime":"2019-08-24T14:15:22Z","delFlag":"string"}]}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RListCrmReport](#schemarlistcrmreport)|

<a id="opIdgetByKeys"></a>

## GET 上报详情

GET /ade/crmReport/detail

上报详情

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|empCode|query|string| 是 |none|
|insCode|query|string| 是 |none|
|customerCode|query|string| 是 |none|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"createBy":0,"createTime":"2019-08-24T14:15:22Z","updateBy":0,"updateTime":"2019-08-24T14:15:22Z","params":{"property1":{},"property2":{}},"tenantId":"string","id":0,"insCode":"string","customerCode":"string","period":"string","num":"string","empCode":"string","deptCode":"string","postCode":"string","reportTime":"2019-08-24T14:15:22Z","delFlag":"string"}}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RCrmReport](#schemarcrmreport)|

<a id="opIddelete_3"></a>

## DELETE 删除

DELETE /ade/crmReport/delete

删除

> Body 请求参数

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|clientId|header|string| 否 |none|
|body|body|[CrmReport](#schemacrmreport)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|

# 数据模型

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|boolean|false|none||数据对象|

<h2 id="tocS_CrmReport">CrmReport</h2>

<a id="schemacrmreport"></a>
<a id="schema_CrmReport"></a>
<a id="tocScrmreport"></a>
<a id="tocscrmreport"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|createBy|integer(int64)|false|none||创建者|
|createTime|string(date-time)|false|none||创建时间|
|updateBy|integer(int64)|false|none||更新者|
|updateTime|string(date-time)|false|none||更新时间|
|params|object|false|none||请求参数|
|» **additionalProperties**|object|false|none||none|
|tenantId|string|false|none||租户编号|
|id|integer(int64)|false|none||none|
|insCode|string|false|none||none|
|customerCode|string|false|none||none|
|period|string|false|none||none|
|num|string|false|none||none|
|empCode|string|false|none||none|
|deptCode|string|false|none||none|
|postCode|string|false|none||none|
|reportTime|string(date-time)|false|none||none|
|delFlag|string|false|none||none|

<h2 id="tocS_RCrmReport">RCrmReport</h2>

<a id="schemarcrmreport"></a>
<a id="schema_RCrmReport"></a>
<a id="tocSrcrmreport"></a>
<a id="tocsrcrmreport"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "tenantId": "string",
    "id": 0,
    "insCode": "string",
    "customerCode": "string",
    "period": "string",
    "num": "string",
    "empCode": "string",
    "deptCode": "string",
    "postCode": "string",
    "reportTime": "2019-08-24T14:15:22Z",
    "delFlag": "string"
  }
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[CrmReport](#schemacrmreport)|false|none||none|

<h2 id="tocS_RListCrmReport">RListCrmReport</h2>

<a id="schemarlistcrmreport"></a>
<a id="schema_RListCrmReport"></a>
<a id="tocSrlistcrmreport"></a>
<a id="tocsrlistcrmreport"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "tenantId": "string",
      "id": 0,
      "insCode": "string",
      "customerCode": "string",
      "period": "string",
      "num": "string",
      "empCode": "string",
      "deptCode": "string",
      "postCode": "string",
      "reportTime": "2019-08-24T14:15:22Z",
      "delFlag": "string"
    }
  ]
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||消息状态码|
|msg|string|false|none||消息内容|
|data|[[CrmReport](#schemacrmreport)]|false|none||数据对象|

