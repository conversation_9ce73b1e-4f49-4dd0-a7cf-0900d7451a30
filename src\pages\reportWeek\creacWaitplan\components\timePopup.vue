<template>
  <view class="timePopup">
    <GlobalPopup ref="popupRef">
      <view style="padding: 16px; height: 350px">
        <view class="timePopup-header">
          <view style="font-size: 14px; color: #869199" @click="closePopup">
            取消
          </view>
          <view style="font-size: 18px"> 计划拜访时间 </view>
          <view style="font-size: 14px; color: #2551f2" @click="confirmTime">
            确认
          </view>
        </view>
        <view class="timePopup-content">
          <nut-tabs v-model="state.value">
            <nut-tab-pane title="开始时间" pane-key="1">
              <nut-date-picker
                type="datetime"
                v-model="state.currentTime"
                :min-date="state.startMinDate"
                :max-date="state.startMaxDate"
                :three-dimensional="false"
                :show-toolbar="false"
                @change="startChange"
              ></nut-date-picker>
            </nut-tab-pane>
            <nut-tab-pane title="结束时间" pane-key="2">
              <nut-date-picker
                v-model="state.completeTime"
                type="hour-minute"
                :three-dimensional="false"
                :show-toolbar="false"
                @change="endChange"
              ></nut-date-picker>
            </nut-tab-pane>
          </nut-tabs>
        </view>
      </view>
    </GlobalPopup>
  </view>
</template>
<script setup>
import { reactive, ref } from "vue";
import Taro from "@tarojs/taro";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
const popupRef = ref(null);
const emit = defineEmits(["confirm-time"]);
const state = reactive({
  starTtimetext: "开始时间",
  endTime: "结束时间",
  value: "1",
  startMinDate: new Date(dayjs().add(1, "day").startOf("day")),

  visitTime: new Date(dayjs().add(1, "day").startOf("day")),
  // startMinDate: new Date(dayjs().startOf("day")),
  // visitTime: new Date(dayjs().startOf("day")),
  startMaxDate: new Date(dayjs().add(1, "day").startOf("day").add(1, "month")),
  yearMD: "",
  sm: "",
  completeTime: new Date(dayjs().add(1, "day").startOf("day").add(2, "minute")),

  currentTime: new Date(dayjs().add(1, "day").startOf("day")),
  // currentTime: new Date(dayjs().startOf("day")),
});

const startChange = ({ columnIndex, selectedValue, selectedOptions }) => {
  state.visitTime = `${selectedValue[0]}-${selectedValue[1]}-${selectedValue[2]} ${selectedValue[3]}:${selectedValue[4]}`;
  state.yearMD = `${selectedValue[0]}-${selectedValue[1]}-${selectedValue[2]}`;
  state.starTtimetext = state.visitTime;
  const time = dayjs(state.visitTime).add(2, "minute");
  state.completeTime = new Date(dayjs(time).format("YYYY-MM-DD HH:mm"));
};

const confirmTime = () => {
  const start = dayjs(state.visitTime);
  const end = dayjs(state.completeTime);
  if (end < start) {
    return Taro.showModal({
      content: "结束时间不能小于开始时间",
      showCancel: false,
    });
  }
  state.visitTime = dayjs(state.visitTime).format("YYYY-MM-DD HH:mm");
  state.completeTime = dayjs(state.completeTime).format("YYYY-MM-DD HH:mm");
  emit("confirm-time", state.visitTime, state.completeTime);
  popupRef.value.close();
};
const closePopup = () => {
  popupRef.value.close();
};

const open = (start, end) => {
  if (start) {
    state.visitTime = new Date(start);
    state.currentTime = new Date(start);
    state.completeTime = new Date(end);
  }

  popupRef.value.open();
};

defineExpose({
  open,
});
</script>
<style lang="scss">
.timePopup {
  .timePopup-header {
    color: #1d212b;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
  }
}
</style>
