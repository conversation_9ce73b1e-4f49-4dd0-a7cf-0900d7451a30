import HTTPREQUEST from "@/servers/http";
// 拜访目的
// 机构拜访列表
export const institutionalVisitsList = (params) => {
  return HTTPREQUEST.get(
    "/msr/api/visit/institution/list",
    params,
    "application/x-www-form-urlencoded"
  );
};

// 机构拜访详情
export const institutionalVisitsDetail = (params) => {
  return HTTPREQUEST.post(`/msr/api/visit/institution/detail`, params);
};

// 签到签退

export const sign = (params) => {
  return HTTPREQUEST.post("/msr/api/visit/sign", params);
};

// 上传文件
export const upload = (params) => {
  return HTTPREQUEST.post("/msr/file/upload", params);
};

export const institutionAdd = (params) => {
  return HTTPREQUEST.post("/msr/api/visit/institution/add", params);
};

export const calendarList = (params) => {
  return HTTPREQUEST.get("/msr/api/visit/calendar/list", params);
};

// 拜访目的
export const visitPurpose = () => {
  return HTTPREQUEST.get("/plt/dict/data/list?dictType=visit_purpose");
};

// 拜访类型
export const visitType = () => {
  return HTTPREQUEST.get("/plt/dict/data/list?dictType=visit_type");
};

export const institutioUsers = (params) => {
  return HTTPREQUEST.get("/crm/api/visit/institution/users", params);
};

export const institutionSearch = (params) => {
  return HTTPREQUEST.get("/mdm/crm/institutions", params);
};
// // 取消拜访计划

export const institutioCancel = (params) => {
  return HTTPREQUEST.get("/msr/api/visit/institution/cancel", params);
};

export const customerSearch = (params) => {
  return HTTPREQUEST.get("/mdm/crm/customers", params);
};
// 拜访负责人的协访人列表
export const assistUsers = (params) => {
  return HTTPREQUEST.get("/crm/api/visit/institution/assist/users", params);
};

export const instituionAddCustomer = (params) => {
  return HTTPREQUEST.post("/crm/api/visit/institution/add/customer", params);
};
// export async function institutionProducts(params){
//   return HTTPREQUEST({
//     url: '/mdm/api/visit/products',
//     method: 'post',
//     data: params,
//   })
// }
export const institutionProducts = (params) => {
  return HTTPREQUEST.post("/msr/api/visit/products", params);
};
// 下属拜访列表
export const institutionSublist = (params) => {
  return HTTPREQUEST.get("/msr/api/visit/institution/sub/list", params);
};

// export async function visitCustomerCancel(params){
//   return HTTPREQUEST({
//     url: '/crm/api/visit/customer/cancel',
//     method: 'get',
//     data:params,
//   })
// }

export const visitCustomerCancel = (params) => {
  return HTTPREQUEST.get("/crm/api/visit/customer/cancel", params);
};

export const institutionValidCustomer = (params) => {
  return HTTPREQUEST.post("/crm/api/visit/institution/valid/customer", params);
};

export const getCurrentTime = (params) => {
  return HTTPREQUEST.get("/msr/api/visit/get/current/time", params);
};

export const customerLastContent = (params) => {
  return HTTPREQUEST.post("/crm/api/visit/customer/last/content", params);
};
export const validIsNotSignOut = (params) => {
  return HTTPREQUEST.get(
    "/msr/api/visit/institution/valid/is_not_sign_out",
    params
  );
};

// // 院外拜访补充说明接口
// export async function signOutAddRemark(params){
//   return HTTPREQUEST({
//     url: '/msr/api/visit/sign/add/remark',
//     method: 'post',
//     data: params,
//   })
// }

export const subUserSearch = (params) => {
  return HTTPREQUEST.get("/crm/api/visit/sub_visit_user/search", params);
};

export const customerV2 = (params) => {
  return HTTPREQUEST.post("/crm/api/visit/institution/add/customer/v2", params);
};

export const institutionSearch2 = (params) => {
  return HTTPREQUEST.get("/crm/api/visit/institution/search", params);
};
export const visitDistance = (params) => {
  return HTTPREQUEST.get("/msr/api/visit/distance", params);
};
export const getCustomers = (params) => {
  return HTTPREQUEST.post(
    `/msr/api/visit/customers?pageNum=${params.pageNum || 1}&pageSize=${
      params.pageSize || 20
    }`,
    params
  );
};

export const hrSubEmp = (params) => {
  return HTTPREQUEST.post("/msr/sys/sub-emp", params);
};

export const assistPurpose = (params) => {
  return HTTPREQUEST.get("/plt/dict/data/list?dictType=assist_purpose", params);
};
export const getAssistUser = (params) => {
  return HTTPREQUEST.get("/msr/sys/emp", params);
};

export const addVisiInfo = (params) => {
  return HTTPREQUEST.post("/msr/api/visit/institution/add/visit_info", params);
};
export const assistMessage = (params) => {
  return HTTPREQUEST.post("/msr/assist/sys/message", params);
};
export const statsIndex = () => {
  return HTTPREQUEST.get("/msr/stats/index");
};

export const imageUrl = (url) => {
  return HTTPREQUEST.get(`/fle/oss-file/tenant/${url}`);
};
export const crmSignOutTime = () => {
  return HTTPREQUEST.get(
    `/plt/config/list?pageNum=1&pageSize=10&configKey=crm.sign.out.time`
  );
};

export const crmSinInM = () => {
  return HTTPREQUEST.get(
    `/plt/config/list?pageNum=1&pageSize=10&configKey=crm.sing.in.m`
  );
};

export const filtering = (params) => {
  return HTTPREQUEST.post(`/plt/sensitive/filtering?matchType=2`, params);
};

// 添加拜访信息
export const visitInfoApi = (params) => {
  return HTTPREQUEST.post(`/msr/api/visit/institution/add/visit_info`, params);
};

// 获取上次沟通内容
/*
* visitInstitutionId 当前拜访ID
institutionCode 机构编码
visitUserHrCode 拜访人工号
* */
export const lastCommunicationApi = (params) => {
  return HTTPREQUEST.post(`/msr/api/visit/ins/last_communication`, params);
};

// 产品分类 下拉框
export const dicProCateApi = () => {
  return HTTPREQUEST.get(`/plt/dict/data/list?dictType=product_classification`);
};

// 获取推广信息系统参数
export const getProductInfoConfig = () => {
  return HTTPREQUEST.get(`/plt/config/configKey/sys.product.info`);
};

export const getDailyReport = () => {
  return HTTPREQUEST.get(`/plt/config/configKey/sys.daily.report`);
};
