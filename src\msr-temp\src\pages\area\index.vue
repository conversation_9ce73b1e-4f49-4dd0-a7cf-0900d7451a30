<!-- 机构列表页 -->
<template>
  <view class="arae">
    <SearchBar
      @seacrh-top="topInsSearch"
      @filters="insFilters"
      placeholder="请输入医疗机构名称"
      v-show="state.currentTab === '0'"
      :active="state.activeFlag"
    />
    <SearchBar
      @seacrh-top="topCusSearch"
      @filters="cusFilters"
      placeholder="请输入机构名称/客户名称/科室名称"
      v-show="state.currentTab === '1'"
      :active="state.cusActiveFlag"
    />
    <SearchBar
      @seacrh-top="topApproveSearch"
      @filters="approveFilters"
      placeholder="请输入关键字搜索"
      v-show="state.currentTab === '3' || state.currentTab === '4'"
      :filters="false"
    />
    <view>
      <nut-tabs
        v-model="state.currentTab"
        backgroud="#FFFFFF"
        @change="tabChange"
      >
        <nut-tab-pane title="辖区下机构" pane-key="0">
          <InsList
            ref="insListRef"
            :tab="state.currentTab"
            @change-pcdvalue="changePcdvalue"
            :appcodeIns="props.appcodeIns"
            :sysJuInsDelObj="state.sysJuInsDelObj"
            :insNoTitleClick="insNoTitleClick"
            :insNoTitleCancel="insNoTitleCancel"
          />
        </nut-tab-pane>
        <nut-tab-pane title="辖区下客户" pane-key="1">
          <CusList
            ref="cusListRef"
            :tab="state.currentTab"
            @change-filter="changeFilter"
            @goAppraise="goAppraise"
            @noTitleClick="noTitleClick"
            :appcodeCust="props.appcodeCust"
            :sysJuCustDelObj="state.sysJuCustDelObj"
          />
        </nut-tab-pane>
        <nut-tab-pane title="我的审批" pane-key="3">
          <ApproveList ref="approveListRef" processType="bpm_jur_apply" />
        </nut-tab-pane>
      </nut-tabs>
    </view>
    <Oval
      title="认领"
      @click-oval="addClaimProduct"
      v-if="
        state.currentTab === '0' &&
        state.sysJuInsFlag === '1' &&
        !isAis
      "
    />
    <Oval
      title="认领"
      @click-oval="addClaimProduct"
      v-if="
        state.currentTab === '1' &&
        state.sysJuCustFlag === '1'
      "
    />

    <custom-popup
      :show-popup-visible="state.showFooterPopupVisible"
      :closeable="false"
      @click-overlay="state.showFooterPopupVisible = false"
    >
      <institution-type
        source="area"
        @customerRouter="customerRouter"
        @clickTitle="state.showFooterPopupVisible = false"
      />
    </custom-popup>
  </view>
  <GlobalDialog ref="globalDialogRef" @confirm="delConfirm">
    <view style="font-size: 16px; padding: 4px 24px">
      <view style="color: #3535354">
        点击确认提交机构解绑审批，进入我的审批查看审批单状态
      </view>
      <view style="color: red"> 请注意：机构下绑定客户将一并解绑 </view>
    </view>
  </GlobalDialog>
</template>
<script setup>
import Taro, { useRouter, useDidShow } from "@tarojs/taro";
import { ref, onMounted, reactive } from "vue";
import InsList from "./ins/instList.vue";
import CusList from "./customer/cusList.vue";
import SearchBar from "../../pages/components/searchBar";
import Oval from "../../pages/components/oval";
import plus from "../../images/inst_plus.png";
import Approve from "./approve/index";
import CustomPopup from "../../pages/components/customPopup/index";
import InstitutionType from "../../pages/institutionalVisits/components/institutionType.vue";
import { appCode, isAis } from "../../utils/content";
import ApproveList from "@/bpm-temp/src/pages/approve_list/index.vue";

import {
  jurCustomerUnique,
  jurInsUnique,
  sysJuInsApi,
  sysJucustApi,
  sysJuInsDelApi,
  sysJucustDelApi,
} from "../../api/area.js";
import GlobalDialog from "../components/globalDialog";
const props = defineProps({
  appcodeIns: {
    type: String,
    default: "",
  },
  appcodeCust: {
    type: String,
    default: "",
  },
});
const router = useRouter();
const globalDialogRef = ref(null);
const insListRef = ref(null);
const cusListRef = ref(null);
const approveRef = ref(null);
const visible1 = ref(false);
const approveListRef = ref(null);
const state = reactive({
  infoDefault: {},
  showFooterPopupVisible: false,
  sysJuInsFlag: "0", // 0否1是
  sysJuCustFlag: "0", // 0否1是
  currentTab: "0",
  activeFlag: false,
  cusActiveFlag: false,
  sysJuInsDelObj: {},
  sysJuCustDelObj: {},
});

const noTitleClick = (bol, item) => {
  visible1.value = bol;
};
const goAppraise = (item) => {
  console.log(item, "--item");
  state.infoDefault = item;
  state.showFooterPopupVisible = true;
};

const changePcdvalue = (value) => {
  if (value) {
    state.activeFlag = true;
  } else {
    state.activeFlag = false;
  }
};
const customerRouter = (itemUrl) => {
  Taro.setStorage({ key: "customerQuery", data: state.infoDefault });
  let url = `${itemUrl}?source=area`;
  state.showFooterPopupVisible = false;
  if (
    sessionStorage.getItem("048") === "1" &&
    itemUrl === "/pages/institutionalVisits/sign/signIn"
  ) {
    const info = encodeURIComponent(JSON.stringify(state.infoDefault));
    Taro.navigateTo({
      url: `/pages/institutionalVisits048/sign/signIn?info=${info}&from=area`,
    });
  }else if(sessionStorage.getItem("048") === "1" && itemUrl === "/pages/institutionalVisits/creacWaitplan/index"){
    Taro.navigateTo({
      url: `/pages/institutionalVisits048/creacWaitplan/index?source=area&from=area`,
    });
  } else {
    Taro.navigateTo({ url });
  }
};

const changeFilter = (params) => {
  if (params.insName || params.insDept || params.teachTitle || params.job) {
    state.cusActiveFlag = true;
  } else {
    state.cusActiveFlag = false;
  }
};
const topInsSearch = (search) => {
  insListRef.value.topSearch(search);
};

const topCusSearch = (search) => {
  cusListRef.value.topSearch(search);
};
const insFilters = () => {
  insListRef.value.insFilters();
};
const cusFilters = (search) => {
  cusListRef.value.cusFilters(search);
};

const topApproveSearch = (search) => {
  // approveRef.value.topSearch(search);
  approveListRef.value?.topSearch(search);
};
const tabChange = (tab = false) => {
  if (tab == 1) state.currentTab = "1";
  if (state.currentTab == "0") {
    insListRef.value.initInsList();
  }
  if (state.currentTab == "1") {
    cusListRef.value.initCusList();
  }
  if (state.currentTab == "3") {
    // approveRef.value.initList();
    approveListRef.value?.initList();
  }
};

const addClaimProduct = () => {
  let url = "";
  if (state.currentTab == "0") url = `/pages/area/ins/search`;
  else url = `/pages/area/customer/search`;

  Taro.navigateTo({
    url,
  });
};

// const getJurCustomerUnique = async () => {
//   const res = await jurCustomerUnique();
//   Taro.setStorage({ key: "jurCustomerUnique", data: res.msg });
// };
const getJurInsUnique = async () => {
  const res = await jurInsUnique();
  Taro.setStorage({ key: "jurInsUnique", data: res.msg });
};

const getSysJuInsApi = async () => {
  const res = await sysJuInsApi();
  Taro.setStorage({ key: "sysJuInsObj", data: JSON.parse(res.msg) });
  state.sysJuInsFlag = JSON.parse(res.msg).settingStatus;
};

const getSysJuInsDeltApi = async () => {
  const res = await sysJuInsDelApi();
  state.sysJuInsDelObj = JSON.parse(res.msg);
};

const getSysJuCustApi = async () => {
  const res = await sysJucustApi();
  Taro.setStorage({ key: "sysJuCustObj", data: JSON.parse(res.msg) });
  state.sysJuCustFlag = JSON.parse(res.msg).settingStatus;
};

const getSysJuCusDeltApi = async () => {
  const res = await sysJucustDelApi();
  state.sysJuCustDelObj = JSON.parse(res.msg);
};
const delConfirm = () => {
  insListRef.value.delConfirm();
};
const insNoTitleClick = async () => {
  globalDialogRef.value.open();
};

const insNoTitleCancel = () => {
  globalDialogRef.value.cancel();
};
useDidShow(() => {
  tabChange(router.params.currentTab);
});
onMounted(() => {
  // getJurCustomerUnique()
  getJurInsUnique();
  getSysJuInsApi();
  getSysJuInsDeltApi();

  getSysJuCustApi();
  getSysJuCusDeltApi();
});
</script>

<style lang="scss">
input::placeholder {
  color: yellow;
}

.arae {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100vw;
  overflow: hidden;
  .searchBar {
    background: #fff;
  }

  .nut-tabs__titles-item__line {
    background: blue;
  }

  .nut-tab-pane {
    background-color: transparent;
    padding: 18px 0px;
  }
  .nut-tab-pane {
    padding: 0 0;
  }

  .nut-searchbar__input-bar.nut-searchbar__input-bar_clear::placeholder {
    font-size: 12px;
  }
}
</style>
