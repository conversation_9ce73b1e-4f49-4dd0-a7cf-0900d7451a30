<template>
  <view>
    <view class="visitCalendar">
      <view style="display: flex; align-items: center" @click="openCalendar">
        <view class="calendarIcon">
          <img
            :src="calendar"
            alt=""
            style="width: 24px; height: 24px; display: inline-block"
          />
        </view>
        <!-- <view style="margin-right: 10px"
          >{{ dayjs(props.visitTime).format("MM/DD") }} -
          {{ dayjs(props.visitEndTime).format("MM/DD") }}</view
        > -->
        <view style="margin-right: 10px">填报日:</view>
        <view style="margin-right: 10px">{{ getTime() }}</view>
      </view>
      <!-- <view class="button-group">
        <view
          class="buttons"
          v-for="item in state.buttonArray"
          :key="item.code"
          @click="buttonGroup(item.code)"
          :class="state.buttonFlag === item.code ? 'button-active' : ''"
          >{{ item.value }}</view
        >
      </view> -->
    </view>

    <view>
      <nut-calendar
        v-model:visible="state.show"
        is-auto-back-fill
        :type="getCalendarType()"
        :default-value="state.currentTime"
        :start-date="state.stateDate"
        :end-date="state.endDate"
        @close="closeCalendar"
        @choose="choose"
        @select="selectDate"
      >
      </nut-calendar>
    </view>
  </view>
</template>
<script setup>
import { onMounted, reactive, nextTick } from "vue";
import calendar from "../../../images/calendar.png";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { useDidShow } from "@tarojs/taro";
const emit = defineEmits(["chage-time"]);
const props = defineProps({
  visitTime: {
    type: String,
    default: "",
  },
  visitEndTime: {
    type: String,
    default: "",
  },
  type: {
    type: Number,
    default: 0,
  },
});

const state = reactive({
  show: false,
  stateDate: dayjs().subtract(1, "month").startOf("month").format("YYYY-MM-DD"),
  endDate: null,
  time: dayjs().format("YYYY-MM-DD"),
  buttonFlag: "",
  buttonArray: [
    { value: "今天", code: "1" },
    { value: "本周", code: "2" },
    { value: "下周", code: "3" },
  ],
  currentTime: dayjs().format("YYYY-MM-DD"), // one模式下使用单个日期
});

// 获取日历类型
const getCalendarType = () => {
  return "one";
};

// 获取指定日期所在周的周五
const getFridayOfWeek = (date) => {
  const dayOfWeek = date.day(); // 0=周日, 1=周一, ..., 6=周六

  // 计算到本周五需要的天数偏移
  let daysToFriday;

  if (dayOfWeek === 0) { // 周日
    daysToFriday = 5; // 周日到周五需要5天
  } else if (dayOfWeek <= 5) { // 周一到周五
    daysToFriday = 5 - dayOfWeek; // 计算到周五的天数
  } else { // 周六
    daysToFriday = -1; // 周六到周五需要回退1天
  }

  return date.add(daysToFriday, 'day');
};

// 获取指定日期所在周的下周四
const getNextThursdayOfWeek = (date) => {
  const friday = getFridayOfWeek(date);
  return friday.add(6, 'day'); // 周五 + 6天 = 下周四
};

// 获取指定日期所在周的上周五
const getLastFridayOfWeek = (date) => {
  const friday = getFridayOfWeek(date);
  return friday.subtract(7, 'day'); // 当前周五 - 7天 = 上周五
};

// 获取指定日期所在周的周四
const getThursdayOfWeek = (date) => {
  const dayOfWeek = date.day(); // 0=周日, 1=周一, ..., 6=周六

  // 计算到本周四需要的天数偏移
  let daysToThursday;

  if (dayOfWeek === 0) { // 周日
    daysToThursday = 4; // 周日到周四需要4天
  } else if (dayOfWeek <= 4) { // 周一到周四
    daysToThursday = 4 - dayOfWeek; // 计算到周四的天数
  } else { // 周五、周六
    daysToThursday = 7 - dayOfWeek + 4; // 到下周四
  }

  return date.add(daysToThursday, 'day');
};

// 根据type计算默认时间范围
const getDefaultTimeRange = () => {
  const today = dayjs();
  console.log('getDefaultTimeRange called with type:', props.type, 'today:', today.format("YYYY-MM-DD"));

  if (props.type === 0) {
    // type === 0: 本周五到下周四
    const friday = getFridayOfWeek(today);
    const nextThursday = getNextThursdayOfWeek(today);
    const result = [friday.format("YYYY-MM-DD"), nextThursday.format("YYYY-MM-DD")];
    console.log('type 0 result:', result);
    return result;
  } else if (props.type === 1) {
    // type === 1: 上周五到本周四
    const lastFriday = getLastFridayOfWeek(today);
    const thursday = getThursdayOfWeek(today);
    const result = [lastFriday.format("YYYY-MM-DD"), thursday.format("YYYY-MM-DD")];
    console.log('type 1 result:', result);
    return result;
  } else if (props.type === 2) {
    // type === 2: 当天
    const result = [today.format("YYYY-MM-DD"), today.format("YYYY-MM-DD")];
    console.log('type 2 result:', result);
    return result;
  }

  console.log('getDefaultTimeRange: unknown type, returning default');
  return [today.format("YYYY-MM-DD"), today.format("YYYY-MM-DD")];
};

// 根据点击的日期和type计算时间范围
const calculateTimeRangeFromDate = (clickedDate) => {
  console.log('calculateTimeRangeFromDate input:', clickedDate); // 调试用

  // 处理不同的日期格式
  let dateStr;
  if (Array.isArray(clickedDate)) {
    dateStr = clickedDate[3] || clickedDate[0]; // 尝试获取日期字符串
  } else {
    dateStr = clickedDate; // 直接使用
  }

  console.log('parsed dateStr:', dateStr); // 调试用

  // 确保日期字符串有效
  if (!dateStr) {
    console.error('Invalid dateStr:', dateStr);
    return [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  }

  const date = dayjs(dateStr);
  console.log('dayjs parsed date:', date.format('YYYY-MM-DD')); // 调试用

  // 检查日期是否有效
  if (!date.isValid()) {
    console.error('Invalid date parsed:', dateStr);
    return [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  }

  if (props.type === 0) {
    // type === 0: 点击日期所属的周五到下周四
    const friday = getFridayOfWeek(date);
    const nextThursday = getNextThursdayOfWeek(date);
    return [friday.format("YYYY-MM-DD"), nextThursday.format("YYYY-MM-DD")];
  } else if (props.type === 1) {
    // type === 1: 点击日期所属的上周五到点击日期的周四
    const lastFriday = getLastFridayOfWeek(date);
    const thursday = getThursdayOfWeek(date);
    return [lastFriday.format("YYYY-MM-DD"), thursday.format("YYYY-MM-DD")];
  } else if (props.type === 2) {
    // type === 2: 当天
    return [date.format("YYYY-MM-DD"), date.format("YYYY-MM-DD")];
  }

  return [date.format("YYYY-MM-DD"), date.format("YYYY-MM-DD")];
};

const choose = (list) => {
  console.log('choose event data:', list); // 调试用，查看数据结构
  console.log('choose event data type:', typeof list); // 调试用
  console.log('choose event data isArray:', Array.isArray(list)); // 调试用

  // 在one模式下，处理不同的数据结构
  let clickedDate;
  if (Array.isArray(list)) {
    // 如果是数组，尝试获取日期字符串
    console.log('list[0]:', list[0]);
    console.log('list[3]:', list[3]);
    clickedDate = list[3] || list[0] || list;
  } else {
    // 如果不是数组，直接使用
    clickedDate = list;
  }
  console.log('final clicked date:', clickedDate);

  // 根据type自动计算时间范围
  if (props.type === 2) {
    // type === 2: 拜访达成，选择当天
    emit("chage-time", clickedDate, clickedDate);
  } else {
    // type === 0 或 1: 根据点击的日期自动计算时间范围
    const calculatedRange = calculateTimeRangeFromDate(clickedDate);
    emit("chage-time", calculatedRange[0], calculatedRange[1]);
  }

  state.buttonFlag = "";
};
const getTime = () => {
  console.log('getTime called with type:', props.type);

  // 实时计算当前的时间范围段
  const currentRange = getDefaultTimeRange();
  console.log('getTime calculated range:', currentRange);

  if (currentRange && currentRange.length >= 2) {
    const startDate = currentRange[0];
    const endDate = currentRange[1];

    if (startDate === endDate) {
      // 单天显示
      return dayjs(startDate).format("MM/DD");
    } else {
      // 范围显示
      return (
        dayjs(startDate).format("MM/DD") +
        "-" +
        dayjs(endDate).format("MM/DD")
      );
    }
  } else {
    // 如果计算失败，回退到props值
    console.log('getTime fallback to props:', props.visitTime, props.visitEndTime);
    if (props.visitTime == props.visitEndTime) {
      return dayjs(props.visitTime).format("MM/DD");
    } else {
      return (
        dayjs(props.visitTime).format("MM/DD") +
        "-" +
        dayjs(props.visitEndTime).format("MM/DD")
      );
    }
  }
};
const openCalendar = () => {
  state.show = true;
  // 从上一个月的第一天开始
  state.stateDate = dayjs().subtract(1, "month").startOf("month").format("YYYY-MM-DD");
  state.endDate = dayjs().add(2, "year").format("YYYY-MM-DD");
};


// 关闭日历
const closeCalendar = () => {
  state.show = false;
};

// 选择日期（用于单选模式的中间状态）
const selectDate = (date) => {
  console.log('select event data:', date); // 调试用
  // 在one模式下，select事件可能不需要特殊处理
  // 主要逻辑在choose事件中处理
};

const buttonGroup = (code) => {
  state.buttonFlag = code;
  let startDate, endDate;

  if (code === "1") {
    // 今天
    startDate = endDate = dayjs().format("YYYY-MM-DD");
    state.currentTime = startDate;
  } else if (code === "2") {
    // 本周
    let currentDate = dayjs();
    if (currentDate.day() == 0) {
      currentDate = currentDate.subtract(1, "day");
    }
    let monday = currentDate.day(1);
    let sunday = currentDate.day(7);
    startDate = monday.format("YYYY-MM-DD");
    endDate = sunday.format("YYYY-MM-DD");
    state.currentTime = startDate; // one模式下只设置开始日期
  } else if (code === "3") {
    // 下周
    const currentDate = dayjs();
    const nextMonday = currentDate.add(1, "week").day(1);
    const nextSunday = currentDate.add(1, "week").day(7);
    startDate = nextMonday.format("YYYY-MM-DD");
    endDate = nextSunday.format("YYYY-MM-DD");
    state.currentTime = startDate; // one模式下只设置开始日期
  }

  emit("chage-time", startDate, endDate);
};

// 初始化默认时间
const initDefaultTime = () => {
  // 总是根据type计算并设置默认时间范围，不管父组件是否已经有值
  const defaultRange = getDefaultTimeRange();
  if (defaultRange && defaultRange.length >= 2) {
    if (props.type === 2) {
      emit("chage-time", defaultRange[0], defaultRange[0]);
    } else {
      emit("chage-time", defaultRange[0], defaultRange[1]);
    }
  } else {
    // 如果获取默认范围失败，使用当天
    const today = dayjs().format("YYYY-MM-DD");
    emit("chage-time", today, today);
  }
};

// 重置到默认时间（供父组件调用）
const resetToDefault = () => {
  return new Promise((resolve) => {
    console.log('resetToDefault called with type:', props.type);

    // 使用nextTick确保props.type已经更新
    nextTick(() => {
      // 根据当前type计算默认时间范围
      const defaultRange = getDefaultTimeRange();
      console.log('resetToDefault calculated range:', defaultRange);

      if (defaultRange && defaultRange.length >= 2) {
        // 更新currentTime
        state.currentTime = dayjs(defaultRange[0]).format("YYYY-MM-DD");

        // 直接emit正确的时间范围，不通过choose函数
        if (props.type === 2) {
          emit("chage-time", defaultRange[0], defaultRange[0]);
        } else {
          emit("chage-time", defaultRange[0], defaultRange[1]);
        }
      } else {
        // 如果获取默认范围失败，使用当天
        const today = dayjs().format("YYYY-MM-DD");
        state.currentTime = today;
        emit("chage-time", today, today);
      }

      resolve();
    });
  });
};

// 暴露方法给父组件
defineExpose({
  resetToDefault
});

// 组件挂载时初始化
useDidShow(() => {
  console.log('visitCalendar useDidShow with type:', props.type);

  // 根据当前type计算并设置默认时间范围
  const defaultRange = getDefaultTimeRange();
  console.log('useDidShow calculated range:', defaultRange);

  if (defaultRange && defaultRange.length >= 2) {
    // 更新currentTime
    state.currentTime = defaultRange[0];

    // 直接emit正确的时间范围
    if (props.type === 2) {
      emit("chage-time", defaultRange[0], defaultRange[0]);
    } else {
      emit("chage-time", defaultRange[0], defaultRange[1]);
    }
  } else {
    // 如果获取默认范围失败，使用当天
    const today = dayjs().format("YYYY-MM-DD");
    state.currentTime = today;
    emit("chage-time", today, today);
  }

  // 调试：打印当前type和默认时间范围
  console.log('Calendar component mounted with type:', props.type);
  // const defaultRange = getDefaultTimeRange();
  console.log('Default time range:', defaultRange);

  // 测试日期计算逻辑 - 测试一周的每一天
  const testDates = [
    dayjs('2024-01-14'), // 周日
    dayjs('2024-01-15'), // 周一
    dayjs('2024-01-16'), // 周二
    dayjs('2024-01-17'), // 周三
    dayjs('2024-01-18'), // 周四
    dayjs('2024-01-19'), // 周五
    dayjs('2024-01-20'), // 周六
  ];

  testDates.forEach(testDate => {
    console.log(`\n测试日期: ${testDate.format('YYYY-MM-DD dddd')}`);
    console.log('本周五:', getFridayOfWeek(testDate).format('YYYY-MM-DD dddd'));
    console.log('本周四:', getThursdayOfWeek(testDate).format('YYYY-MM-DD dddd'));
    console.log('下周四:', getNextThursdayOfWeek(testDate).format('YYYY-MM-DD dddd'));
    console.log('上周五:', getLastFridayOfWeek(testDate).format('YYYY-MM-DD dddd'));
  });
});
</script>
<style lang="scss">
.visitCalendar {
  display: flex;
  align-items: center;
  // justify-content: space-between;
  background: #fff;
  padding: 16px;
  .calendarIcon {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    background: #f3f4f5;
    text-align: center;
    line-height: 45px;
    margin-right: 10px;
  }
  .button-group {
    width: 162px;
    height: 32px;
    border-radius: 52px;
    background: #f3f4f5;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    .buttons {
      width: 52px;
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      border-radius: 32px;
      color: #4e595e;
    }
    .button-active {
      color: #2551f2;
      background: #fff;
    }
  }
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--choose::after {
  background-color: #92a8f8;
}
.nut-calendar
  .nut-calendar__content
  .nut-calendar__panel
  .nut-calendar__days
  .nut-calendar__day--choose {
  color: #2551f2;
}
</style>
