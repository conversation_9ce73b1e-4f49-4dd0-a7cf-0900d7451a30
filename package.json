{"name": "athena-crm", "version": "1.0.29", "private": true, "description": "athena-taro-front", "templateInfo": {"name": "vue3-NutUI4", "typescript": false, "css": "sass"}, "scripts": {"build:h5": "taro build --type h5", "build:dev": "cross-env NODE_ENV=development taro build --type h5 --mode development", "build:lark": "taro build --type lark", "dev:h5": "npm run build:h5 -- --watch --mode development", "dev:lark": "npm run build:lark -- --watch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@form-create/element-ui": "^2.6.2", "@form-create/vant": "3", "@nutui/icons-vue-taro": "^0.0.9", "@nutui/nutui-taro": "^4.0.4", "@tarojs/components": "3.6.18", "@tarojs/helper": "3.6.18", "@tarojs/plugin-framework-vue3": "3.6.18", "@tarojs/plugin-html": "3.6.18", "@tarojs/plugin-platform-alipay": "3.6.18", "@tarojs/plugin-platform-h5": "3.6.18", "@tarojs/plugin-platform-jd": "3.6.18", "@tarojs/plugin-platform-lark": "^1.1.4", "@tarojs/plugin-platform-qq": "3.6.18", "@tarojs/plugin-platform-swan": "3.6.18", "@tarojs/plugin-platform-tt": "3.6.18", "@tarojs/plugin-platform-weapp": "3.6.18", "@tarojs/runtime": "3.6.18", "@tarojs/shared": "3.6.18", "@tarojs/taro": "^3.6.18", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "js-base64": "^3.7.5", "vant": "^4.9.9", "vconsole": "^3.15.1", "vue": "^3.2.40", "vue3-hash-calendar": "^1.1.3"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/cli": "3.6.18", "@tarojs/taro-loader": "3.6.18", "@tarojs/webpack5-runner": "3.6.18", "@types/node": "^18.15.11", "@types/webpack-env": "^1.13.6", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/compiler-sfc": "^3.2.40", "autoprefixer": "^10.4.20", "babel-preset-taro": "3.6.18", "css-loader": "3.4.2", "eslint": "^8.12.0", "eslint-config-taro": "3.6.18", "eslint-plugin-vue": "^8.0.0", "postcss": "^8.4.49", "style-loader": "1.3.0", "stylelint": "9.3.0", "tailwindcss": "^3.4.15", "ts-node": "^10.9.1", "typescript": "^4.1.0", "unplugin-vue-components": "^0.23.0", "vue-loader": "^17.0.0", "webpack": "^5.78.0"}}