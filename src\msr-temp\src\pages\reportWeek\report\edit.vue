<template>
  <view class="report-edit" style="background-color: #fff">
    <view class="form-container">
      <CellClick
        @selectClick="selectCusClick"
        :value="state.customerName"
        :required="true"
        label="客户"
        placeholder="请选择"
        :phRight="true"
        :disabled="!!query.customerCode"
      />
      <nut-cell
        title="医疗机构"
        class="readonly-cell"
        :desc="state.institutionName"
      ></nut-cell>
      <nut-cell
        title="科室"
        class="readonly-cell"
        :desc="state.insDeptName"
      ></nut-cell>
      <!-- <nut-cell
        title="上报周期"
        class="readonly-cell"
        desc=""
      ></nut-cell> -->
      <nut-cell
        :title="`上报日(本周五到下周四)`"
        class="readonly-cell"
        :desc="state.period"
      ></nut-cell>

      <view v-if="query.type === '1'" class="form-item flex justify-between items-center">
        <view class="form-label" style="width: 120px;">
          <span class="required">*</span>诊疗体验数量
        </view>
        <nut-input
          v-model="state.num"
          type="digit"
          placeholder="请输入大于0的整数"
          :disabled="state.isDisabled"
          input-align="right"
        />
      </view>

      <view class="form-item flex justify-between items-center">
        <view class="form-label" style="width: 120px;">
          <span class="required">*</span>会议场次
        </view>
        <nut-input
          v-model="state.meetingNum"
          type="digit"
          placeholder="请输入大于0的整数"
          :disabled="state.isDisabled"
          input-align="right"
        />
      </view>
      <view style="color: red; font-size: 14px;">请注意：每周四14:00截止数据录入</view>

      <!-- 审批状态提示 -->
      <view v-if="state.approveStatus === '2'" class="status-tip confirmed">
        <text>✓ 此数据已确认，不可再次提交</text>
      </view>
      <view v-else-if="state.approveStatus === '3'" class="status-tip rejected">
        <text>✗ 此数据已被拒绝，可重新提交</text>
      </view>
    </view>

    <FooterButton
      :text="getButtonText()"
      @click-button="saveReport"
      :disabled="state.isDisabled || state.approveStatus === '2'"
    />
  </view>

  <!-- 客户选择弹窗 -->
  <CusPopup ref="cusPopupRef" @select-cus="selectCus" />
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import dayjs from "dayjs";
import Taro, { useDidHide, useDidShow, useRouter } from "@tarojs/taro";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import CellClick from "../../../pages/components/cellClick/index.vue";
import CusPopup from "../components/cusPopup";
import { getDailyReport } from "../../../api/institutionalVisitsApi";
import { addOrUpdateReport, getReportDetail } from "./api.js";

// 初始化状态
const state = reactive({
  id: null,
  insCode: '',
  customerCode: '',
  customerName: '',
  institutionName: '',
  insDeptName: '',
  periodText: '',
  reportDate: '',
  num: '',
  meetingNum: '', // 会议场次
  empCode: '',
  deptCode: '',
  postCode: '',
  isDisabled: false, // 保存按钮是否禁用
  period: '',
  approveStatus: '1' // 审批状态：1-待审批，2-已确认，3-已拒绝
});

const router = useRouter();
const query = router.params;
const cusPopupRef = ref(null);

// 获取按钮文本
const getButtonText = () => {
  if (state.isDisabled) {
    return '本周数据已截止填报';
  }
  if (state.approveStatus === '2') {
    return '已确认数据不可提交';
  }
  return '提交';
};

// 保存上报数据
const saveReport = async () => {
  // 检查是否已禁用
  if (state.isDisabled) {
    Taro.showToast({
      title: '本周数据已截止填报',
      icon: 'none'
    });
    return;
  }

  // 检查数据是否已确认，已确认的数据不可以再提交
  if (state.approveStatus === '2') {
    Taro.showToast({
      title: '已确认的数据不可以再提交',
      icon: 'none'
    });
    return;
  }

  // 验证客户选择
  if (!state.customerCode) {
    Taro.showToast({
      title: '请选择客户',
      icon: 'none'
    });
    return;
  }

  // 验证诊疗体验数量输入 - 只有当type为'1'时才需要验证
  if (query.type === '1') {
    if (!state.num) {
      Taro.showToast({
        title: '请输入诊疗体验数量',
        icon: 'none'
      });
      return;
    }

    // 验证诊疗体验数量格式
    const numValue = parseInt(state.num);
    if (isNaN(numValue) || numValue <= 0) {
      Taro.showToast({
        title: '诊疗体验数量必须是大于0的整数',
        icon: 'none'
      });
      return;
    }
  }

  // 验证会议场次输入
  if (!state.meetingNum) {
    Taro.showToast({
      title: '请输入会议场次',
      icon: 'none'
    });
    return;
  }

  // 验证会议场次格式
  const meetingNumValue = parseInt(state.meetingNum);
  if (isNaN(meetingNumValue) || meetingNumValue <= 0) {
    Taro.showToast({
      title: '会议场次必须是大于0的整数',
      icon: 'none'
    });
    return;
  }

  // 构造请求参数
  const params = {
    id: state.id,
    insCode: state.insCode,
    customerCode: state.customerCode,
    period: state.reportDate,
    eventNum: parseInt(state.meetingNum).toString(), // 会议场次对应eventNum字段
    empCode: state.empCode,
    deptCode: state.deptCode,
    postCode: state.postCode,
    type: query.type || '1', // 使用URL参数中的type，默认为'1'
    approveStatus: state.approveStatus || '1' // 默认待审批状态
  };

  // 只有当type为'1'时才添加诊疗体验数量
  if (query.type === '1') {
    params.num = parseInt(state.num).toString();
  }

  try {
    // 调用API保存数据
    const res = await addOrUpdateReport(params);
    if (res?.code === 200) {
      Taro.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 返回上一页并刷新数据
      setTimeout(() => {
        Taro.navigateBack({
          delta: 1
        });
      }, 1000);
    } else {
      Taro.showToast({
        title: res.msg || '保存失败',
        icon: 'none'
      });
    }
  } catch (error) {
    Taro.showToast({
      title: '保存失败',
      icon: 'none'
    });
    console.error('保存失败:', error);
  }
};

// 客户选择相关方法
const selectCusClick = () => {
  if (query.customerCode) return; // 如果已有客户信息，不允许修改
  cusPopupRef.value.open();
};

const selectCus = async (obj) => {
  state.customerCode = obj.customerCode;
  state.customerName = obj.customerName;
  state.insCode = obj.insCode;
  state.institutionName = obj.insName;
  state.insDeptName = obj.insDeptName;

  // 获取用户信息
  const initHrInfo = Taro.getStorageSync("initHrInfo");
  if (initHrInfo) {
    state.empCode = initHrInfo.empCode;
    state.deptCode = initHrInfo.orgList?.[0]?.deptCode || '';
    state.postCode = initHrInfo.orgList?.[0]?.code || '';
  }

  // 获取已有数据（如果存在）
  loadReportDetail();
};

// 检查是否在填报时间范围内
const checkTimeRange = () => {
  const now = dayjs();
  const dayOfWeek = now.day(); // 0为周日，1为周一，...，6为周六
  const hour = now.hour();
  const minute = now.minute();
  
  // 周四14:00之后到周日午夜前，禁止填报
  if (dayOfWeek === 4 && hour >= 14) {
    state.isDisabled = true;
    return;
  }
  
  if (dayOfWeek === 5 || dayOfWeek === 6 || (dayOfWeek === 0 && (hour < 23 || (hour === 23 && minute <= 59)))) {
    state.isDisabled = true;
    return;
  }
  
  state.isDisabled = false;
};

// 获取周期文本
const getPeriodText = () => {
  // 上周日到本周四
  const now = dayjs();
  const dayOfWeek = now.day();
  const lastSunday = now.subtract(dayOfWeek, 'day').subtract(7, 'day');
  const thisThursday = now.subtract(dayOfWeek - 4, 'day');
  return `${lastSunday.format('YYYY-MM-DD')} 至 ${thisThursday.format('YYYY-MM-DD')}`;
};

// 获取上报日期（固定为本周四）
const getReportDate = () => {
  const now = dayjs();
  // 计算本周四的日期
  const dayOfWeek = now.day(); // 0为周日，1为周一，...，4为周四
  // 计算距离本周四还有几天
  const daysToThursday = 4 - dayOfWeek;
  // 获取本周四的日期
  const thursday = now.add(daysToThursday, 'day');
  return thursday.format('YYYY-MM-DD');
};



useDidShow(() => {
  Taro.setNavigationBarTitle({
    title: "周计划上报"
  });

  // 获取日报信息
  getDailyReport().then(res => {
    if (res?.code === 200) {
      state.isDisabled = res.msg === '0';
    } else {
      // Taro.showToast({
      //   title: res.msg || '获取日报失败',
      //   icon: 'none'
      // });
    }
  });

  // 检查填报时间范围
  // checkTimeRange();
  
  // 设置周期文本
  state.periodText = getPeriodText();
  
  // 设置默认上报日期
  state.reportDate = getReportDate();
  
  // 初始化数据
  if (query.customerCode) {
    state.customerCode = query.customerCode;
    state.customerName = decodeURIComponent(query.customerName);
    state.insCode = query.insCode;
    state.institutionName = decodeURIComponent(query.institutionName);
    state.insDeptName = decodeURIComponent(query.insDeptName);

    // 获取用户信息
    const initHrInfo = Taro.getStorageSync("initHrInfo");
    if (initHrInfo) {
      state.empCode = initHrInfo.empCode;
      state.deptCode = initHrInfo.orgList?.[0]?.deptCode || '';
      state.postCode = initHrInfo.orgList?.[0]?.code || '';
    }

    // 获取已有数据（如果存在）
    loadReportDetail();
  } else {
    // 没有客户信息时，只获取用户信息，等待用户选择客户
    const initHrInfo = Taro.getStorageSync("initHrInfo");
    if (initHrInfo) {
      state.empCode = initHrInfo.empCode;
      state.deptCode = initHrInfo.orgList?.[0]?.deptCode || '';
      state.postCode = initHrInfo.orgList?.[0]?.code || '';
    }
  }
});

// 加载已有数据
const loadReportDetail = async () => {
  try {
    const res = await getReportDetail({
      empCode: state.empCode,
      insCode: state.insCode,
      customerCode: state.customerCode,
      type: query.type || '1' // 使用URL参数中的type，默认为'1'
    });
    console.log('获取详情成功:', res);
    if (res?.code === 200 && res?.data) {
      const data = res.data;
      state.id = data.id;
      state.num = data.num;
      state.meetingNum = data.eventNum; // eventNum对应会议场次
      state.period = data.period;
      state.approveStatus = data.approveStatus || '1'; // 设置审批状态
    }
  } catch (error) {
    console.error('获取详情失败:', error);
  }
};
</script>

<style lang="scss">
.report-edit {
  .form-container {
    padding: 16px;
    
    .readonly-cell {
      margin-bottom: 12px;
    }
    
    .form-item {
      margin-bottom: 20px;
      
      .form-label {
        font-size: 15px;
        margin-bottom: 10px;
        color: #333;
        
        .required {
          color: #ff0000;
          margin-right: 4px;
        }
      }
      
      .nut-input {
        /* border: 1px solid #e5e5e5;
        border-radius: 4px;
        padding: 10px; */
      }
    }
  }

  // 审批状态提示样式
  .status-tip {
    margin: 12px 0;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;

    &.confirmed {
      background-color: #f0f9ff;
      color: #0369a1;
      border: 1px solid #bae6fd;
    }

    &.rejected {
      background-color: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    &.pending {
      background-color: #fffbeb;
      color: #d97706;
      border: 1px solid #fed7aa;
    }
  }
}
</style>