<template>
  <view class="institutionList">
    <scroll-view
      :scroll-y="true"
      style="height: 500px"
      @scrolltolower="scrolltolower"
      :scroll-top="state.scrollTop"
    >
      <view
        style="
          position: absolute;
          top: 40%;
          left: 50%;
          transform: translate(-50%);
        "
        v-if="!props.institutionList.length"
      >
        <img :src="noData" alt="" style="width: 180px; height: 180px" />
        <view style="text-align: center; color: #86909c; font-size: 14px"
          >暂无数据</view
        >
      </view>
      <view
        v-for="item in props.institutionList"
        :key="item.id"
        class="visitsList"
        :class="{ 'batch-mode': props.batchMode, 'selected': isSelected(item), 'disabled': !canSelectItem(item) }"
        @click="handleCardClick(item)"
      >
        <!-- 批量模式下的勾选框 -->
        <view v-if="props.batchMode" class="checkbox-container" @click.stop="toggleSelect(item)">
          <img :src="isSelected(item) ? radioIcon2 : radioIcon1"
               class="checkbox-icon"
               :class="{ 'disabled': !canSelectItem(item) }" />
        </view>

        <view
          class="visitsList_left"
          style="display: flex; align-items: center"
        >
          <view style="display: flex; align-items: center">
            <view
              style="
                margin-right: 4px;
                display: flex;
                align-items: center;
                font-weight: 500;
              "
              >{{ item.customerName }}
            </view>
          </view>
          <view
            :class="getColor(item.approveStatus)"
            style="font-size: 14px; width: 60px; text-align: right"
          >
            {{ getApproveStatusName(item.approveStatus) }}
          </view>
        </view>
        <nut-divider dashed :style="{ color: '#E5E6EB', margin: '12px 0' }" />
        <view class="visitsList_bottom">
          <view>
            <view>
              {{ item.insName }}
              <text>{{ item.insMdmCode }}</text>
            </view>
            <view>{{ item.period }}</view>
            <view v-if="props.type === '-1'">是否有诊疗体验数: {{ item.num }}</view>
            <view v-if="props.type !== '-1'">诊疗体验数量: {{ item.num }}</view>
            <view v-if="props.type !== '-1'">会议场次: {{ item.eventNum }}</view>
            <view>{{ item.empName }} {{ item.deptName }}</view>
            <view>最新提交时间: {{ item.reportTime }}</view>
            <view>最新确认时间: {{ item.approveTime }}</view>
          </view>
        </view>
      </view>
      <view
        v-if="
          props.institutionList.length > 0 &&
          props.institutionList.length == props.total
        "
        style="color: #869199; text-align: center"
        >已全部加载完毕</view
      >
    </scroll-view>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import noData from "../../../images/no-data.png";
import radioIcon1 from "./../radio-icon-unchecked.png";
import radioIcon2 from "./../radio_checked_icon.png";
import { defineProps, onMounted, reactive } from "vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";

const emit = defineEmits(["scrolltolower", "toggle-select"]);
const props = defineProps({
  institutionList: {
    type: Array,
    default: [],
  },
  activeIndex: {
    type: Number,
    default: 0,
  },
  total: {
    type: Number,
    default: 0,
  },
  type: {
    type: String,
    default: '1',
  },
  // 批量审批相关props
  batchMode: {
    type: Boolean,
    default: false,
  },
  selectedItems: {
    type: Array,
    default: () => [],
  },
  canSelect: {
    type: Function,
    default: () => true,
  },
});
const state = reactive({
  scrollTop: 0,
});
const scrolltolower = () => {
  emit("scrolltolower");
};

const getApproveStatusName = (code) => {
  if (code === "1") {
    return "已提交";
  }
  if (code === "2") {
    return "已确认";
  }
  if (code === "0") {
    return "未提交";
  }
}

const getColor = (code) => {
  if (code === "0") return "color-3";

  if (code === "1") return "color-1";

  if (code === "2") return "color-0";

}

// 判断项目是否被选中
const isSelected = (item) => {
  return props.selectedItems.includes(item.id);
};

// 判断项目是否可以被选择
const canSelectItem = (item) => {
  return props.canSelect(item);
};

// 切换选择状态
const toggleSelect = (item) => {
  if (!canSelectItem(item)) return;
  emit('toggle-select', item);
};

// 处理卡片点击
const handleCardClick = (item) => {
  if (props.batchMode) {
    // 批量模式下点击卡片进行选择
    toggleSelect(item);
  } else {
    // 普通模式下跳转到编辑页面
    navigateToEdit(item);
  }
};

// 跳转到编辑页面
const navigateToEdit = (item) => {
  if(props.type === '-1') {
    Taro.navigateTo({
         url: `/pages/institutionalVisits048/sign/signIn?id=${item.id}&mode=edit`,
    });
  }else {
    Taro.navigateTo({
      url: `/pages/reportWeek/report/edit?customerCode=${item.customerCode}&customerName=${encodeURIComponent(item.customerName)}&insCode=${item.insCode}&institutionName=${encodeURIComponent(item.insName)}&insDeptName=${encodeURIComponent(item.insDeptName)}&type=${props.type}`
    });
  }
};

const visitTime = (item) => {
  if (item.status === "0" || item.status === "3" || item.status === "4") {
    const start = dayjs(item.visitTime).format("MM-DD HH:mm");
    const end = dayjs(item.completeTime).format("HH:mm");
    return `${start} - ${end}`;
  } else if (item.status === "1") {
    const signIn = dayjs(item.signInTime).format("MM-DD HH:mm");
    const signOut = dayjs(item.signOutTime).format("HH:mm");
    return `${signIn} - ${signOut}`;
  } else if (item.status === "2") {
    return dayjs(item.signInTime).format("MM-DD HH:mm");
  } else {
  }
};
</script>

<style lang="scss" scoped>
.tag-f {
  margin-left: 4px;
  color: #f77234;
  background: #fff3e8;
  min-width: 48px;

  font-size: 12px;

  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  height: 20px;
  line-height: 20px;
}
.tag-s {
  margin-left: 4px;
  color: #2551f2;
  background: #e8f0ff;
  min-width: 48px;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  height: 20px;
  line-height: 20px;
}
.institutionList {
  color: #1d212b;
  padding: 0 16px;
  padding-bottom: 25px;
  .visitsList {
    padding: 12px 16px 12px 16px;
    background-color: #ffffff;
    border-radius: 8px;
    margin-bottom: 12px;
    .visitsList_left {
      font-size: 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .visitsList_bottom {
      font-size: 13px;
      color: #869199;
      display: flex;
      justify-content: space-between;
      img {
        display: inline-block;
        width: 16px;
        height: 16px;
        vertical-align: text-bottom;
        margin-right: 4px;
      }
      .m_b {
        // margin-bottom: 8px;
      }
    }
  }
  .nut-divider {
    margin: 16px 0;
  }
  .color-0 {
    color: rgb(55, 118, 246);
  }

  .color-1 {
    color: rgba(0, 181, 120);
  }

  .color-2 {
    color: rgba(255, 180, 47);
  }

  .color-4 {
    color: rgba(243, 47, 41);
  }
}

/* 批量模式样式 */
.visitsList.batch-mode {
  position: relative;
  padding-left: 50px; /* 为勾选框留出空间 */
  cursor: pointer;
  transition: all 0.2s ease;

  &.selected {
    /* 去掉蓝色描边，保持原有样式 */
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    .checkbox {
      cursor: not-allowed;
    }
  }

  &:hover:not(.disabled) {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.checkbox-container {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.checkbox-icon {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}


</style>
