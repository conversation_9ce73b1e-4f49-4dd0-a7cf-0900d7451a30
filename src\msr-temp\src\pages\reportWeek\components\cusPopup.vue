<!-- 该文件为客户拜访选择客户弹窗 -->
<template>
  <view class="cusPopup">
    <GlobalPopup ref="popupRef">
      <TopSearch
        class="topSearch"
        @top-search="search"
        placeholder="请输入客户名称/机构名称/科室"
      />
      <nut-radio-group
        v-if="state?.cusOption?.length != 0"
        style="height: 66vh"
        v-model="state.radioChecked"
        text-position="left"
      >
        <GlobalNutRadio
          v-for="item in state.cusOption"
          :value="item.insCustId"
          :key="item.insCustId"
        >
          <template #name>
            <view>
              <text>{{ item.customerName }}</text>
            </view>
            <view style="color: #869199; font-size: 14px; margin-top: 5px">
              <text>{{ item.insName }} </text>
              <nut-divider direction="vertical" />
              <text>{{ item.insDeptName }}</text>
            </view>
          </template>
        </GlobalNutRadio>
      </nut-radio-group>
      <template v-else>
        <view class="null-box">
          <view class="null-img-box" v-if="searched">
            <img class="null-img" :src="NullIcon" alt="" />
            <view class="null-text">搜索无数据</view>
            <view style="color: red;font-size: 12px;margin-top: 12px;">注：您搜索的客户可能不在您辖区内，请去“我的辖区-》辖区下客户”点右下角 认领客户；</view>
          </view>
          <view v-else class="null-not-searched">您当前辖区下无客户，请去“我的辖区-》辖区下客户”点右下角认领客户；</view>

        </view>

      </template>

      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
    </GlobalPopup>
  </view>
</template>
<script setup>
import { reactive, ref, onMounted } from "vue";
import Taro from "@tarojs/taro";
import GlobalPopup from "../../../pages/components/globalPopup/globalPopup.vue";
import TopSearch from "../../../pages/components/topSearch/topSearch.vue";
import GlobalNutRadio from "../../../pages/components/globalNutRadio/globalNutRadio.vue";
import { getCustomers } from "../../../api/institutionalVisitsApi";
import NullIcon from "../../../images/null-icon.png";
const emit = defineEmits(["select-cus"]);
const initHrInfo = Taro.getStorageSync("initHrInfo");
const state = reactive({
  radioChecked: "",
  search: "",
  cusOption: [],
});
const popupRef = ref(null);
const searched = ref(false);

const search = (name) => {
  state.radioChecked = "";
  // if (name) {
    state.search = name;
    getCusOption();
  // } else {
    // state.cusOption = [];
  // }
};

const getCusOption = async () => {
  Taro.showLoading({
    mask: true,
    title: "加载中",
  });
  const params = {
    search: state.search,
    postIdList: initHrInfo.postIdList,
    selfPostIdList: initHrInfo.selfPostIdList,
    subPostIdList: initHrInfo.subPostIdList,
    pageNum: 1,
    pageSize: 20
  };
  const res = await getCustomers(params);
  if (params.search.length === 0){
    searched.value = false;
  } else{
    searched.value = true;
  }
  // console.log('search状态',searched.value);
  Taro.hideLoading();

  state.cusOption = res.data || [];
};
onMounted(async() => {
  // 初始化时加载所有数据
  await getCusOption();
});
const confirm = () => {
  const arr = state.cusOption.filter(
    (item) => item.insCustId === state.radioChecked
  );
  emit("select-cus", arr[0]);
  popupRef.value.close();
};
const open = () => {
  popupRef.value.open();
};

defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.cusPopup {
  // padding-bottom: 30px;
  // background: #f4f5f7;
  .nut-radio-group {
    height: 500px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 0 16px;
  }
  .nut-radio {
    overflow: hidden;
    overflow-y: scroll;
    background: #fff;
  }

  .footer-comfirm {
    // height: 60px;
    // padding: 16px;
    line-height: 60px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .nut-searchbar__search-input
    .nut-searchbar__input-inner-absolute
    .nut-searchbar__input-bar {
    padding-right: 0;
  }
  .null-img-box {
    position: absolute;
    top: 48%;
    left: 50%;
    transform: translate(-50%, -50%); /* 使用CSS3的transform属性来居中 */
  }
  .null-img {
    width: 200px;
    height: 200px;
  }
  .topSearch {
    margin-top: 16px;
  }
  .null-box {
    box-sizing: border-box;
    height: 60vh;
    padding-top: 30px;
    position: relative;
  }
  .null-text {
    text-align: center;
    color: #86909c;
    font-size: 16px;
    // padding-top: 30px;
  }
  .null-not-searched{
    text-align: center;
    color: #86909c;
    font-size: 16px;
    padding: 30px;
  }
}
</style>
