<template>
  <view class="representPopup">
    <GlobalPopup ref="popupRef">
      <view style="padding: 20px; text-align: center">筛选代表</view>
      <TopSearch
        @top-search="search"
        placeholder="请输入姓名/工号搜索"
        ref="topSearchRef"
      />
      <nut-checkbox-group style="max-height: 60vh" v-model="state.checked">
        <GlobalNutCheckbox
          v-for="item in state.cusOption"
          :value="item.id"
          :key="item.id"
          style="color: red"
        >
          <template #name>
            <view
              :class="{
                'color-label': state.checked.includes(item.empCode),
              }"
            >
              <text>{{ item.empName }}</text>
              <text>({{ item.empCode }}) </text>
              <nut-divider direction="vertical" />
              <text>{{ item.deptName }}</text>
            </view>
          </template>
        </GlobalNutCheckbox>
      </nut-checkbox-group>
      <view class="footer-comfirm" style="color: #2551f2" @click="confirm"
        >确定</view
      >
      <FooterButtonTwo
        rightText="确定"
        @click-left="clickButtonLeft"
        @click-right="clickButtonRight"
        leftText="重置"
        :plainFlag="false"
        leftColor=""
        leftTextColor="#4E595E"
      />
    </GlobalPopup>
  </view>
</template>
<script setup>
import Taro from "@tarojs/taro";
import { reactive, ref, watch } from "vue";
import GlobalPopup from "@/pages/components/globalPopup/globalPopup.vue";
import TopSearch from "@/pages/components/topSearch/topSearch.vue";
import GlobalNutCheckbox from "@/pages/components/globalNutCheckbox/globalNutCheckbox.vue";
import { hrSubEmp } from "@/api/institutionalVisitsApi";
import FooterButtonTwo from "@/pages/components/footerButtonTwo/footerButtonTwo";
const topSearchRef = ref(null);
const initHrInfo = Taro.getStorageSync("initHrInfo");
const emit = defineEmits(["select-pre"]);
const state = reactive({
  checked: [],
  search: "",
  cusOption: [],
});
const popupRef = ref(null);

const search = async (name) => {
  state.search = name;
  state.cusOption = [];
  await getCusOption();
};

const getCusOption = async () => {
  const params = {
    search: state.search,
    subPostIdList: initHrInfo.subPostIdList,
    groupStr: "post",
  };
  const res = await hrSubEmp(params);
  state.cusOption = res.data;
};
const clickButtonLeft = () => {
  topSearchRef.value.setSearch();
  state.search = "";
  state.checked = [];
  getCusOption();
};

const clickButtonRight = () => {
  const arr = state.cusOption.filter((item) => state.checked.includes(item.id));
  emit("select-pre", arr);
  state.search = "";
  state.checked = [];
  state.cusOption = [];
  popupRef.value.close();
};
const open = (list) => {
  popupRef.value.open();
  console.log("list", list, initHrInfo.subUserCodeList);
  if (initHrInfo.subUserCodeList.length == list.length) {
    state.checked = [];
  } else {
    state.checked = list;
  }

  getCusOption();
};

defineExpose({
  open,
});
</script>
<style lang="scss">
.representPopup {
  // padding-bottom: 60px;
  // background: #f4f5f7;
  .nut-checkbox {
    margin-right: 0;
  }
  .nut-checkbox-group {
    font-size: 16px;
    height: 400px;
    overflow: hidden;
    overflow-y: scroll;
    padding: 16px;
    justify-content: space-between;
  }
  .nut-checkbox--reverse .nut-checkbox__label {
    font-size: 16px;
  }
  .footer-comfirm {
    height: 60px;
    padding: 16px;
    text-align: center;
    border-top: 8px solid #f4f5f7;
  }
  .searchBar .nut-searchbar__search-input {
    border: none;
  }
  .nut-searchbar__search-input {
    background: #f3f4f5;
  }
  .color-label {
    color: #2551f2;
  }
}
</style>
