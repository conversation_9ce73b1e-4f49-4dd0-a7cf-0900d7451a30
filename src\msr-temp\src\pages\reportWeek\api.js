import httpRequest from "@/servers/http";

/**
 * 拜协访详情（客户）
 * @param {Object} params - VisitDetailBo 结构
 * @param {number} params.visitInstitutionId - 拜访 id
 * @param {number} params.assistId - 协访 id
 * @returns {Promise}
 * 接口路径: POST /ade/api/visit/detail
 * 示例: getVisitDetail({ visitInstitutionId: 123, assistId: 456 })
 */
export function getVisitDetail(params) {
  return httpRequest.post("/ade/api/visit/detail", params);
}

/**
 * 添加客户拜访信息
 * @param {Object} params - VisitContentBo 结构，包含 visitInfoBo、visitSignBo
 * @returns {Promise}
 * VisitContentBo 示例：
 * {
 *   visitInfoBo: {
 *     visitPurposeCode: string,
 *     communicateContent: string,
 *     assistRequirement: string,
 *     assistForm: array,
 *     productForms: array,
 *     extendInfo: string
 *   },
 *   visitSignBo: {
 *     // 签到相关字段，详见接口文档
 *   }
 * }
 */
export function addCustomerVisit(params) {
  return httpRequest.post("/ade/api/visit/customer/add", params);
}

/**
 * 获取机构/客户标签
 * @param {Object} params
 * @param {string} params.type - 标签类型（必填）
 * @param {string} params.code - 机构/客户编码（必填）
 * @returns {Promise}
 * 接口路径: GET /ade/api/label
 * 示例: getLabel({ type: 'customer', code: 'xxx' })
 */
export function getLabel(params) {
  return httpRequest.get("/ade/api/label", params);
}

/**
 * 048部门校验
 * @param {Object} params
 * @param {string} deptCode - 部门编码（必填）
 * @returns {Promise}
 * 接口路径: GET /ade/api/check-dept
 * 示例: checkDept({ deptCode: 'xxx' })
 */
export function checkDept(deptCode) {
  return httpRequest.get("/ade/api/check-dept?deptCode=" + deptCode);
}

/**
 * 修改客户拜访信息
 * @param {Object} params - VisitInfoBo 结构
 * @param {number} params.visitInstitutionId - 拜访机构表主键
 * @param {string} params.visitPurposeCode - 拜访目的编码
 * @param {string} params.communicateContent - 实际沟通内容
 * @param {string} params.assistRequirement - 协访需求
 * @param {Array} params.assistForm - 选择协访人
 * @param {Array} params.productForms - 产品推广信息
 * @param {string} params.extendInfo - 拓展信息
 * @returns {Promise}
 * 接口路径: POST /ade/api/visit/customer/update
 * 示例: updateCustomerVisit({ visitInstitutionId: 123, visitPurposeCode: 'xxx', ... })
 */
export function updateCustomerVisit(params) {
  return httpRequest.post("/ade/api/visit/customer/update", params);
}

/**
 * 新增或更新上报API
 * @param {Object} params - 上报数据
 * @param {string} params.empCode - 员工编码
 * @param {string} params.insCode - 机构编码
 * @param {string} params.customerCode - 客户编码
 * @param {string} params.reportDate - 上报日期
 * @param {number} params.num - 诊疗体验数量
 * @param {number} params.meetingNum - 会议场次
 * @param {string} params.deptCode - 部门编码
 * @param {string} params.postCode - 岗位编码
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport
 */
export function addOrUpdateReport(params) {
  return httpRequest.post("/ade/crmReport", params);
}

/**
 * 根据键值获取上报详情API
 * @param {Object} params - 查询参数
 * @param {string} params.empCode - 员工编码
 * @param {string} params.insCode - 机构编码
 * @param {string} params.customerCode - 客户编码
 * @returns {Promise}
 * 接口路径: GET /ade/crmReport/detail
 */
export function getReportDetail(params) {
  return httpRequest.get("/ade/crmReport/detail", params);
}
