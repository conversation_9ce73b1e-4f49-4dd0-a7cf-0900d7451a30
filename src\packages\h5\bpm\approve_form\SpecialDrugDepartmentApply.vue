<template>
  <view class="bg-white px-[16px]">
    <view>
      <view class="text-[#1D212B] text-[14px] py-[10px]">基本信息</view>
      <view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >医疗机构</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insName
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构编码</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.insMdmCode
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >机构药店距离(米)</view
          >
          <view
            class="text-red-500 text-[14px] font-400 text-right"
            v-if="props.formData.distanceTooLong"
            >{{ props.formData.distanceTooLong }}</view
          >
          <view v-else class="text-[#1D212B] text-[14px] font-400 text-right">{{
            props.formData.distance
          }}</view>
        </view>
        <view class="flex items-top mb-[8px] justify-between">
          <view class="text-[#869199] text-[14px] font-400 w-[200px]"
            >地址</view
          >
          <view class="text-[#1D212B] text-[14px] font-400 text-right"
            >{{ props.formData.province }}{{ props.formData.city
            }}{{ props.formData.district }}{{ props.formData.address }}</view
          >
        </view>
      </view>
    </view>
    <view class="pt-[16px]">
      <view class="text-[#1D212B] text-[14px] py-[10px]">药店信息</view>
      <view class="bg-white rounded-[8px] mx-[8px] mb-[16px]">
        <view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >药店名称</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.dsName
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >药店编码</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.dsMdmCode
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >关联药店类型</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.insDsType
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >上级药店</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.superiorDsName
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >上级药店编码</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.superiorDsCode
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >数据采集方式</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.dsSource
            }}</view>
          </view>
          <view class="flex items-top mb-[8px] justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >药店性质</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.dsNature
            }}</view>
          </view>
          <view class="flex items-top justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >药店支付类型</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.dsEconomicStr
            }}</view>
          </view>
          <view class="flex items-top justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >是否HIS对接</view
            >
            <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
              props.formData.hisDocking
            }}</view>
          </view>
          <view class="flex items-top justify-between">
            <view class="text-[#869199] text-[14px] font-400 w-[124px]"
              >科室</view
            >
              <view class="text-[#1D212B] text-[14px] font-400 text-right">
                {{ props.formData?.departmentCodeList?.map(dept => dept.departmentName)?.join('、') || '-' }}
              </view>
          </view>
          <view
            class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"
          >
          </view>
          <view
            v-for="spec in props.formData.specList || props.formData.insDsList"
          >
            <view class="flex items-top mb-[8px] justify-between">
              <view class="text-[#869199] text-[14px] font-400 w-[124px]"
                >品规名称</view
              >
              <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
                spec.value
              }}</view>
            </view>
            <view class="flex items-top mb-[8px] justify-between">
              <view class="text-[#869199] text-[14px] font-400 w-[124px]"
                >是否商务开发</view
              >
              <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
                props.formData.businessContact === "1" ? "是" : "否"
              }}</view>
            </view>
            <view class="flex items-top mb-[8px] justify-between">
              <view class="text-[#869199] text-[14px] font-400 w-[124px]"
                >开发状态</view
              >
              <view class="text-[#1D212B] text-[14px] font-400 text-right">{{
                props.formData.salesStatus
              }}</view>
            </view>

            <view
              class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"
            >
            </view>
          </view>

          <view class="flex flex-col mb-[8px]">
            <view class="text-[#869199] text-[14px] font-400 mb-[16px]"
              >照片(营业执照、经营许可证、门头照)</view
            >
            <view class="flex">
              <img
                v-for="(item, index) in props.formData.ossIdList"
                :key="item"
                :src="item"
                alt=""
                style="width: 80px; height: 80px; margin-right: 4px"
                @click="handlePreview(index)"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
    <nut-image-preview
      :autoplay="0"
      :show="showPreview"
      :images="state.fileList"
      @close="hideImgPreview"
      :init-no="imgPreviewIndex"
    />
  </view>
</template>

<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { onMounted, reactive, ref, computed } from "vue";
import { imageUrl } from "@/bpm-temp/src/api/bpm";

const router = useRouter();
const query = router.params;

const props = defineProps({
  formData: {
    type: Object,
    default: {},
  },
  formType: {
    type: String,
    default: "",
  },
});
const options = ref([]);
const state = reactive({
  info: {},
  specList: [],
  fileList: [],
});
const showPreview = ref(false);
const imgPreviewIndex = ref(0);
const handlePreview = (index) => {
  imgPreviewIndex.value = index;
  showImgPreview();
};
const showImgPreview = () => {
  showPreview.value = true;
};

const hideImgPreview = () => {
  showPreview.value = false;
};

onMounted(() => {
  props.formData = props.formData || {};
  if (props.formData.fileList) {
    props.formData.ossIdList = [];
    props.formData.fileList = JSON.parse(props.formData.fileList);
    props.formData.fileList.forEach(async (i) => {
      const res = await imageUrl(i.ossId);
      props.formData.ossIdList.push(res.data.rows[0].url);
      state.fileList.push({ src: res.data.rows[0].url });
    });
  }
});
</script>

<style lang="scss"></style>
