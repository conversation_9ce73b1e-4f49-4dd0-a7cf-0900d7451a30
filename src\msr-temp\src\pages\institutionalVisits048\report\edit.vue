<template>
  <view class="report-edit" style="background-color: #fff">
    <view class="form-container">
      <nut-cell
        title="客户"
        class="readonly-cell"
        :desc="state.customerName"
      ></nut-cell>
      <nut-cell
        title="医疗机构"
        class="readonly-cell"
        :desc="state.institutionName"
      ></nut-cell>
      <nut-cell
        title="科室"
        class="readonly-cell"
        :desc="state.insDeptName"
      ></nut-cell>
      <!-- <nut-cell
        title="上报周期"
        class="readonly-cell"
        desc=""
      ></nut-cell> -->
      <nut-cell
        :title="`上报日(本周五到下周四)`"
        class="readonly-cell"
        :desc="state.period"
      ></nut-cell>

      <view class="form-item flex justify-between items-center">
        <view class="form-label" style="width: 80px;">
          <span class="required">*</span>数量
        </view>
        <nut-input
          v-model="state.num"
          type="digit"
          placeholder="请输入大于0的整数"
          :disabled="state.isDisabled"
          input-align="right"
        />
      </view>
      <view style="color: red; font-size: 14px;">请注意：每周四14:00截止数据录入</view>
    </view>

    <FooterButton 
      :text="state.isDisabled ? '本周数据已截止填报' : '保存'" 
      @click-button="saveReport" 
      :disabled="state.isDisabled"
    />
  </view>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue";
import dayjs from "dayjs";
import Taro, { useDidHide, useDidShow, useRouter } from "@tarojs/taro";
import FooterButton from "../../../pages/components/footerButton/index.vue";
import { getDailyReport } from "../../../api/institutionalVisitsApi";

// 初始化状态
const state = reactive({
  id: null,
  insCode: '',
  customerCode: '',
  customerName: '',
  institutionName: '',
  insDeptName: '',
  periodText: '',
  reportDate: '',
  num: '',
  empCode: '',
  deptCode: '',
  postCode: '',
  isDisabled: false, // 保存按钮是否禁用
  period: ''
});

const router = useRouter();
const query = router.params;

// 保存上报数据
const saveReport = async () => {
  // 检查是否已禁用
  if (state.isDisabled) {
    Taro.showToast({
      title: '本周数据已截止填报',
      icon: 'none'
    });
    return;
  }

  // 验证数量输入
  if (!state.num) {
    Taro.showToast({
      title: '请输入数量',
      icon: 'none'
    });
    return;
  }

  // 验证数量是否为大于0的整数
  const numValue = parseInt(state.num);
  if (isNaN(numValue) || numValue <= 0) {
    Taro.showToast({
      title: '请输入大于0的整数',
      icon: 'none'
    });
    return;
  }

  // 构造请求参数
  const params = {
    id: state.id,
    insCode: state.insCode,
    customerCode: state.customerCode,
    period: state.reportDate,
    num: numValue.toString(),
    empCode: state.empCode,
    deptCode: state.deptCode,
    postCode: state.postCode
  };

  try {
    // 调用API保存数据
    const res = await addOrUpdateReport(params);
    if (res?.code === 200) {
      Taro.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 返回上一页并刷新数据
      setTimeout(() => {
        Taro.navigateBack({
          delta: 1
        });
      }, 1000);
    }else if (res.data?.code === 200) {
      Taro.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      // 返回上一页并刷新数据
      setTimeout(() => {
        Taro.navigateBack({
          delta: 1
        });
      }, 1000);
    } else {
      Taro.showToast({
        title: res.msg || '保存失败',
        icon: 'none'
      });
    }
  } catch (error) {
    Taro.showToast({
      title: '保存失败',
      icon: 'none'
    });
    console.error('保存失败:', error);
  }
};

// 检查是否在填报时间范围内
const checkTimeRange = () => {
  const now = dayjs();
  const dayOfWeek = now.day(); // 0为周日，1为周一，...，6为周六
  const hour = now.hour();
  const minute = now.minute();
  
  // 周四14:00之后到周日午夜前，禁止填报
  if (dayOfWeek === 4 && hour >= 14) {
    state.isDisabled = true;
    return;
  }
  
  if (dayOfWeek === 5 || dayOfWeek === 6 || (dayOfWeek === 0 && (hour < 23 || (hour === 23 && minute <= 59)))) {
    state.isDisabled = true;
    return;
  }
  
  state.isDisabled = false;
};

// 获取周期文本
const getPeriodText = () => {
  // 上周日到本周四
  const now = dayjs();
  const dayOfWeek = now.day();
  const lastSunday = now.subtract(dayOfWeek, 'day').subtract(7, 'day');
  const thisThursday = now.subtract(dayOfWeek - 4, 'day');
  return `${lastSunday.format('YYYY-MM-DD')} 至 ${thisThursday.format('YYYY-MM-DD')}`;
};

// 获取上报日期（固定为本周四）
const getReportDate = () => {
  const now = dayjs();
  // 计算本周四的日期
  const dayOfWeek = now.day(); // 0为周日，1为周一，...，4为周四
  // 计算距离本周四还有几天
  const daysToThursday = 4 - dayOfWeek;
  // 获取本周四的日期
  const thursday = now.add(daysToThursday, 'day');
  return thursday.format('YYYY-MM-DD');
};

// 新增或更新上报API
const addOrUpdateReport = (params) => {
  return Taro.request({
    url: process.env.TARO_APP_API + '/ade/crmReport',
    method: 'POST',
    data: params,
    header: {
      'Authorization': `Bearer ${Taro.getStorageSync('access_token')}`,
      'clientId': process.env.TARO_APP_CLIENT_ID
    }
  });
};

// 根据键值获取上报详情API
const getReportDetail = (params) => {
  return Taro.request({
    url: process.env.TARO_APP_API + '/ade/crmReport/detail',
    method: 'GET',
    data: params,
    header: {
      'Authorization': `Bearer ${Taro.getStorageSync('access_token')}`,
      'clientId': process.env.TARO_APP_CLIENT_ID
    }
  });
};

useDidShow(() => {
  Taro.setNavigationBarTitle({
    title: "周计划上报"
  });

  // 获取日报信息
  getDailyReport().then(res => {
    if (res?.code === 200) {
      state.isDisabled = res.msg === '0';
    } else {
      // Taro.showToast({
      //   title: res.msg || '获取日报失败',
      //   icon: 'none'
      // });
    }
  });

  // 检查填报时间范围
  // checkTimeRange();
  
  // 设置周期文本
  state.periodText = getPeriodText();
  
  // 设置默认上报日期
  state.reportDate = getReportDate();
  
  // 初始化数据
  if (query.customerCode) {
    state.customerCode = query.customerCode;
    state.customerName = decodeURIComponent(query.customerName);
    state.insCode = query.insCode;
    state.institutionName = decodeURIComponent(query.institutionName);
    state.insDeptName = decodeURIComponent(query.insDeptName);

    // 获取用户信息
    const initHrInfo = Taro.getStorageSync("initHrInfo");
    if (initHrInfo) {
      state.empCode = initHrInfo.empCode;
      state.deptCode = initHrInfo.orgList?.[0]?.deptCode || '';
      state.postCode = initHrInfo.orgList?.[0]?.code || '';
    }
    
    // 获取已有数据（如果存在）
    loadReportDetail();
  }
});

// 加载已有数据
const loadReportDetail = async () => {
  try {
    const res = await getReportDetail({
      empCode: state.empCode,
      insCode: state.insCode,
      customerCode: state.customerCode
    });
    console.log('获取详情成功:', res);
    if (res.data?.code === 200 && res.data?.data) {
      const data = res.data.data;
      state.id = data.id;
      state.num = data.num;
      state.period = data.period;
    }else if (res?.code === 200 && res?.data) {
      const data = res.data;
      state.id = data.id;
      state.num = data.num;
      state.period = data.period;
    }
  } catch (error) {
    console.error('获取详情失败:', error);
  }
};
</script>

<style lang="scss">
.report-edit {
  .form-container {
    padding: 16px;
    
    .readonly-cell {
      margin-bottom: 12px;
    }
    
    .form-item {
      margin-bottom: 20px;
      
      .form-label {
        font-size: 15px;
        margin-bottom: 10px;
        color: #333;
        
        .required {
          color: #ff0000;
          margin-right: 4px;
        }
      }
      
      .nut-input {
        /* border: 1px solid #e5e5e5;
        border-radius: 4px;
        padding: 10px; */
      }
    }
  }
}
</style>