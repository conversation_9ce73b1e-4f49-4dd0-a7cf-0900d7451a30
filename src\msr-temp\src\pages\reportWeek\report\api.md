---
title: athena-cloud
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"
---

# athena-cloud

Base URLs:

# Authentication

# 金项链/标品/athena-tenant-ade/048 客户上报

<a id="opIdaddOrUpdate"></a>

## POST 新增或更新上报

POST /ade/crmReport

新增或更新上报

> Body 请求参数

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string",
  "type": "string",
  "eventNum": "string",
  "approveStatus": "string",
  "periodStartTime": "string",
  "approveTime": "2019-08-24T14:15:22Z",
  "approveEmpCode": "string",
  "productName": "string",
  "productCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                          | 必选 | 说明 |
| ------------- | ------ | ----------------------------- | ---- | ---- |
| Authorization | header | string                        | 否   | none |
| clientId      | header | string                        | 否   | none |
| body          | body   | [CrmReport](#schemacrmreport) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RBoolean](#schemarboolean) |

<a id="opIdgetVisitStatistics"></a>

## POST 获取拜访统计数据

POST /ade/crmReport/visit/statistics

获取拜访统计数据

> Body 请求参数

```json
{
  "period": "string",
  "date": "string",
  "deptIdList": [0],
  "deptCodeList": ["string"],
  "type": "string",
  "approveStatus": "string",
  "querySub": true,
  "query": {
    "pageSize": 0,
    "pageNum": 0,
    "orderByColumn": "string",
    "isAsc": "string"
  }
}
```

### 请求参数

| 名称          | 位置   | 类型                                  | 必选 | 说明 |
| ------------- | ------ | ------------------------------------- | ---- | ---- |
| Authorization | header | string                                | 否   | none |
| clientId      | header | string                                | 否   | none |
| body          | body   | [ReportQueryVo](#schemareportqueryvo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"property1":{},"property2":{}}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                    |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RMapStringObject](#schemarmapstringobject) |

<a id="opIdgetVisitList"></a>

## POST 获取拜访达成列表

POST /ade/crmReport/visit/list

获取拜访达成列表

> Body 请求参数

```json
{
  "period": "string",
  "date": "string",
  "deptIdList": [0],
  "deptCodeList": ["string"],
  "type": "string",
  "approveStatus": "string",
  "querySub": true,
  "query": {
    "pageSize": 0,
    "pageNum": 0,
    "orderByColumn": "string",
    "isAsc": "string"
  }
}
```

### 请求参数

| 名称          | 位置   | 类型                                  | 必选 | 说明 |
| ------------- | ------ | ------------------------------------- | ---- | ---- |
| Authorization | header | string                                | 否   | none |
| clientId      | header | string                                | 否   | none |
| body          | body   | [ReportQueryVo](#schemareportqueryvo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"total":0,"rows":[{"createBy":0,"createTime":"2019-08-24T14:15:22Z","updateBy":0,"updateTime":"2019-08-24T14:15:22Z","params":{"property1":{},"property2":{}},"tenantId":"string","id":0,"insCode":"string","customerCode":"string","period":"string","num":"string","empCode":"string","deptCode":"string","postCode":"string","reportTime":"2019-08-24T14:15:22Z","delFlag":"string","type":"string","eventNum":"string","approveStatus":"string","periodStartTime":"string","approveTime":"2019-08-24T14:15:22Z","approveEmpCode":"string","productName":"string","productCode":"string","insName":"string","insMdmCode":"string","deptName":"string","customerName":"string"}],"code":0,"msg":"string"}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                      |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RTableDataInfoCrmReportVo](#schemartabledatainfocrmreportvo) |

<a id="opIdbatchApproveVisit"></a>

## POST 拜访批量审批

POST /ade/crmReport/visit/batchApprove

拜访批量审批

> Body 请求参数

```json
[0]
```

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明 |
| ------------- | ------ | -------------- | ---- | ---- |
| Authorization | header | string         | 否   | none |
| clientId      | header | string         | 否   | none |
| body          | body   | array[integer] | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RBoolean](#schemarboolean) |

<a id="opIdgetStatistics"></a>

## POST 获取统计数据

POST /ade/crmReport/statistics

获取统计数据

> Body 请求参数

```json
{
  "period": "string",
  "date": "string",
  "deptIdList": [0],
  "deptCodeList": ["string"],
  "type": "string",
  "approveStatus": "string",
  "querySub": true,
  "query": {
    "pageSize": 0,
    "pageNum": 0,
    "orderByColumn": "string",
    "isAsc": "string"
  }
}
```

### 请求参数

| 名称          | 位置   | 类型                                  | 必选 | 说明 |
| ------------- | ------ | ------------------------------------- | ---- | ---- |
| Authorization | header | string                                | 否   | none |
| clientId      | header | string                                | 否   | none |
| body          | body   | [ReportQueryVo](#schemareportqueryvo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"property1":{},"property2":{}}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                    |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RMapStringObject](#schemarmapstringobject) |

<a id="opIdlistAll"></a>

## POST 查询列表

POST /ade/crmReport/list

查询列表

> Body 请求参数

```json
{
  "period": "string",
  "date": "string",
  "deptIdList": [0],
  "deptCodeList": ["string"],
  "type": "string",
  "approveStatus": "string",
  "querySub": true,
  "query": {
    "pageSize": 0,
    "pageNum": 0,
    "orderByColumn": "string",
    "isAsc": "string"
  }
}
```

### 请求参数

| 名称          | 位置   | 类型                                  | 必选 | 说明 |
| ------------- | ------ | ------------------------------------- | ---- | ---- |
| Authorization | header | string                                | 否   | none |
| clientId      | header | string                                | 否   | none |
| body          | body   | [ReportQueryVo](#schemareportqueryvo) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"total":0,"rows":[{"createBy":0,"createTime":"2019-08-24T14:15:22Z","updateBy":0,"updateTime":"2019-08-24T14:15:22Z","params":{"property1":{},"property2":{}},"tenantId":"string","id":0,"insCode":"string","customerCode":"string","period":"string","num":"string","empCode":"string","deptCode":"string","postCode":"string","reportTime":"2019-08-24T14:15:22Z","delFlag":"string","type":"string","eventNum":"string","approveStatus":"string","periodStartTime":"string","approveTime":"2019-08-24T14:15:22Z","approveEmpCode":"string","productName":"string","productCode":"string","insName":"string","insMdmCode":"string","deptName":"string","customerName":"string"}],"code":0,"msg":"string"}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                                                      |
| ------ | ------------------------------------------------------- | ---- | ------------------------------------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RTableDataInfoCrmReportVo](#schemartabledatainfocrmreportvo) |

<a id="opIdbatchApprove"></a>

## POST 批量审批

POST /ade/crmReport/batchApprove

批量审批

> Body 请求参数

```json
[0]
```

### 请求参数

| 名称          | 位置   | 类型           | 必选 | 说明 |
| ------------- | ------ | -------------- | ---- | ---- |
| Authorization | header | string         | 否   | none |
| clientId      | header | string         | 否   | none |
| body          | body   | array[integer] | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RBoolean](#schemarboolean) |

<a id="opIdapprove"></a>

## POST 审批报表

POST /ade/crmReport/approve

审批报表

> Body 请求参数

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string",
  "type": "string",
  "eventNum": "string",
  "approveStatus": "string",
  "periodStartTime": "string",
  "approveTime": "2019-08-24T14:15:22Z",
  "approveEmpCode": "string",
  "productName": "string",
  "productCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                          | 必选 | 说明 |
| ------------- | ------ | ----------------------------- | ---- | ---- |
| Authorization | header | string                        | 否   | none |
| clientId      | header | string                        | 否   | none |
| body          | body   | [CrmReport](#schemacrmreport) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RBoolean](#schemarboolean) |

<a id="opIdgetByKeys"></a>

## GET 上报详情

GET /ade/crmReport/detail

上报详情

### 请求参数

| 名称          | 位置   | 类型   | 必选 | 说明 |
| ------------- | ------ | ------ | ---- | ---- |
| empCode       | query  | string | 是   | none |
| insCode       | query  | string | 是   | none |
| customerCode  | query  | string | 是   | none |
| type          | query  | string | 是   | none |
| Authorization | header | string | 否   | none |
| clientId      | header | string | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"createBy":0,"createTime":"2019-08-24T14:15:22Z","updateBy":0,"updateTime":"2019-08-24T14:15:22Z","params":{"property1":{},"property2":{}},"tenantId":"string","id":0,"insCode":"string","customerCode":"string","period":"string","num":"string","empCode":"string","deptCode":"string","postCode":"string","reportTime":"2019-08-24T14:15:22Z","delFlag":"string","type":"string","eventNum":"string","approveStatus":"string","periodStartTime":"string","approveTime":"2019-08-24T14:15:22Z","approveEmpCode":"string","productName":"string","productCode":"string"}}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                        |
| ------ | ------------------------------------------------------- | ---- | ------------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RCrmReport](#schemarcrmreport) |

<a id="opIddelete_3"></a>

## DELETE 删除

DELETE /ade/crmReport/delete

删除

> Body 请求参数

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string",
  "type": "string",
  "eventNum": "string",
  "approveStatus": "string",
  "periodStartTime": "string",
  "approveTime": "2019-08-24T14:15:22Z",
  "approveEmpCode": "string",
  "productName": "string",
  "productCode": "string"
}
```

### 请求参数

| 名称          | 位置   | 类型                          | 必选 | 说明 |
| ------------- | ------ | ----------------------------- | ---- | ---- |
| Authorization | header | string                        | 否   | none |
| clientId      | header | string                        | 否   | none |
| body          | body   | [CrmReport](#schemacrmreport) | 否   | none |

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true}
```

### 返回结果

| 状态码 | 状态码含义                                              | 说明 | 数据模型                    |
| ------ | ------------------------------------------------------- | ---- | --------------------------- |
| 200    | [OK](https://tools.ietf.org/html/rfc7231#section-6.3.1) | OK   | [RBoolean](#schemarboolean) |

# 数据模型

<h2 id="tocS_PageQuery">PageQuery</h2>

<a id="schemapagequery"></a>
<a id="schema_PageQuery"></a>
<a id="tocSpagequery"></a>
<a id="tocspagequery"></a>

```json
{
  "pageSize": 0,
  "pageNum": 0,
  "orderByColumn": "string",
  "isAsc": "string"
}
```

分页查询实体类

### 属性

| 名称          | 类型           | 必选  | 约束 | 中文名 | 说明                     |
| ------------- | -------------- | ----- | ---- | ------ | ------------------------ |
| pageSize      | integer(int32) | false | none |        | 分页大小                 |
| pageNum       | integer(int32) | false | none |        | 当前页数                 |
| orderByColumn | string         | false | none |        | 排序列                   |
| isAsc         | string         | false | none |        | 排序的方向 desc 或者 asc |

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true
}
```

响应信息主体

### 属性

| 名称 | 类型           | 必选  | 约束 | 中文名 | 说明       |
| ---- | -------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32) | false | none |        | 消息状态码 |
| msg  | string         | false | none |        | 消息内容   |
| data | boolean        | false | none |        | 数据对象   |

<h2 id="tocS_RMapStringObject">RMapStringObject</h2>

<a id="schemarmapstringobject"></a>
<a id="schema_RMapStringObject"></a>
<a id="tocSrmapstringobject"></a>
<a id="tocsrmapstringobject"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "property1": {},
    "property2": {}
  }
}
```

响应信息主体

### 属性

| 名称                       | 类型           | 必选  | 约束 | 中文名 | 说明       |
| -------------------------- | -------------- | ----- | ---- | ------ | ---------- |
| code                       | integer(int32) | false | none |        | 消息状态码 |
| msg                        | string         | false | none |        | 消息内容   |
| data                       | object         | false | none |        | 数据对象   |
| » **additionalProperties** | object         | false | none |        | none       |

<h2 id="tocS_CrmReport">CrmReport</h2>

<a id="schemacrmreport"></a>
<a id="schema_CrmReport"></a>
<a id="tocScrmreport"></a>
<a id="tocscrmreport"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string",
  "type": "string",
  "eventNum": "string",
  "approveStatus": "string",
  "periodStartTime": "string",
  "approveTime": "2019-08-24T14:15:22Z",
  "approveEmpCode": "string",
  "productName": "string",
  "productCode": "string"
}
```

### 属性

| 名称                       | 类型              | 必选  | 约束 | 中文名 | 说明                   |
| -------------------------- | ----------------- | ----- | ---- | ------ | ---------------------- |
| createBy                   | integer(int64)    | false | none |        | 创建者                 |
| createTime                 | string(date-time) | false | none |        | 创建时间               |
| updateBy                   | integer(int64)    | false | none |        | 更新者                 |
| updateTime                 | string(date-time) | false | none |        | 更新时间               |
| params                     | object            | false | none |        | 请求参数               |
| » **additionalProperties** | object            | false | none |        | none                   |
| tenantId                   | string            | false | none |        | 租户编号               |
| id                         | integer(int64)    | false | none |        | none                   |
| insCode                    | string            | false | none |        | 机构编码               |
| customerCode               | string            | false | none |        | 客户编码               |
| period                     | string            | false | none |        | 上报周期               |
| num                        | string            | false | none |        | cx 数量                |
| empCode                    | string            | false | none |        | 上报人工号             |
| deptCode                   | string            | false | none |        | 部门编码               |
| postCode                   | string            | false | none |        | 岗位编码               |
| reportTime                 | string(date-time) | false | none |        | 上报时间               |
| delFlag                    | string            | false | none |        | none                   |
| type                       | string            | false | none |        | 上报类型 1 计划 2 达成 |
| eventNum                   | string            | false | none |        | 会议场次               |
| approveStatus              | string            | false | none |        | 审批状态               |
| periodStartTime            | string            | false | none |        | 上报周期开始时间       |
| approveTime                | string(date-time) | false | none |        | 审批通过时间           |
| approveEmpCode             | string            | false | none |        | 审批通过时间           |
| productName                | string            | false | none |        | 产品名称               |
| productCode                | string            | false | none |        | 产品编码               |

<h2 id="tocS_RCrmReport">RCrmReport</h2>

<a id="schemarcrmreport"></a>
<a id="schema_RCrmReport"></a>
<a id="tocSrcrmreport"></a>
<a id="tocsrcrmreport"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "createBy": 0,
    "createTime": "2019-08-24T14:15:22Z",
    "updateBy": 0,
    "updateTime": "2019-08-24T14:15:22Z",
    "params": {
      "property1": {},
      "property2": {}
    },
    "tenantId": "string",
    "id": 0,
    "insCode": "string",
    "customerCode": "string",
    "period": "string",
    "num": "string",
    "empCode": "string",
    "deptCode": "string",
    "postCode": "string",
    "reportTime": "2019-08-24T14:15:22Z",
    "delFlag": "string",
    "type": "string",
    "eventNum": "string",
    "approveStatus": "string",
    "periodStartTime": "string",
    "approveTime": "2019-08-24T14:15:22Z",
    "approveEmpCode": "string",
    "productName": "string",
    "productCode": "string"
  }
}
```

响应信息主体

### 属性

| 名称 | 类型                          | 必选  | 约束 | 中文名 | 说明       |
| ---- | ----------------------------- | ----- | ---- | ------ | ---------- |
| code | integer(int32)                | false | none |        | 消息状态码 |
| msg  | string                        | false | none |        | 消息内容   |
| data | [CrmReport](#schemacrmreport) | false | none |        | none       |

<h2 id="tocS_ReportQueryVo">ReportQueryVo</h2>

<a id="schemareportqueryvo"></a>
<a id="schema_ReportQueryVo"></a>
<a id="tocSreportqueryvo"></a>
<a id="tocsreportqueryvo"></a>

```json
{
  "period": "string",
  "date": "string",
  "deptIdList": [0],
  "deptCodeList": ["string"],
  "type": "string",
  "approveStatus": "string",
  "querySub": true,
  "query": {
    "pageSize": 0,
    "pageNum": 0,
    "orderByColumn": "string",
    "isAsc": "string"
  }
}
```

### 属性

| 名称          | 类型                          | 必选  | 约束 | 中文名 | 说明                   |
| ------------- | ----------------------------- | ----- | ---- | ------ | ---------------------- |
| period        | string                        | false | none |        | 上报周期               |
| date          | string                        | false | none |        | none                   |
| deptIdList    | [integer]                     | false | none |        | 下级列表               |
| deptCodeList  | [string]                      | false | none |        | 下级列表               |
| type          | string                        | false | none |        | 上报类型 1 计划 2 达成 |
| approveStatus | string                        | false | none |        | 审批状态               |
| querySub      | boolean                       | false | none |        | 查询下属               |
| query         | [PageQuery](#schemapagequery) | false | none |        | 分页查询实体类         |

<h2 id="tocS_CrmReportVo">CrmReportVo</h2>

<a id="schemacrmreportvo"></a>
<a id="schema_CrmReportVo"></a>
<a id="tocScrmreportvo"></a>
<a id="tocscrmreportvo"></a>

```json
{
  "createBy": 0,
  "createTime": "2019-08-24T14:15:22Z",
  "updateBy": 0,
  "updateTime": "2019-08-24T14:15:22Z",
  "params": {
    "property1": {},
    "property2": {}
  },
  "tenantId": "string",
  "id": 0,
  "insCode": "string",
  "customerCode": "string",
  "period": "string",
  "num": "string",
  "empCode": "string",
  "deptCode": "string",
  "postCode": "string",
  "reportTime": "2019-08-24T14:15:22Z",
  "delFlag": "string",
  "type": "string",
  "eventNum": "string",
  "approveStatus": "string",
  "periodStartTime": "string",
  "approveTime": "2019-08-24T14:15:22Z",
  "approveEmpCode": "string",
  "productName": "string",
  "productCode": "string",
  "insName": "string",
  "insMdmCode": "string",
  "deptName": "string",
  "customerName": "string"
}
```

### 属性

| 名称                       | 类型              | 必选  | 约束 | 中文名 | 说明                   |
| -------------------------- | ----------------- | ----- | ---- | ------ | ---------------------- |
| createBy                   | integer(int64)    | false | none |        | 创建者                 |
| createTime                 | string(date-time) | false | none |        | 创建时间               |
| updateBy                   | integer(int64)    | false | none |        | 更新者                 |
| updateTime                 | string(date-time) | false | none |        | 更新时间               |
| params                     | object            | false | none |        | 请求参数               |
| » **additionalProperties** | object            | false | none |        | none                   |
| tenantId                   | string            | false | none |        | 租户编号               |
| id                         | integer(int64)    | false | none |        | none                   |
| insCode                    | string            | false | none |        | 机构编码               |
| customerCode               | string            | false | none |        | 客户编码               |
| period                     | string            | false | none |        | 上报周期               |
| num                        | string            | false | none |        | cx 数量                |
| empCode                    | string            | false | none |        | 上报人工号             |
| deptCode                   | string            | false | none |        | 部门编码               |
| postCode                   | string            | false | none |        | 岗位编码               |
| reportTime                 | string(date-time) | false | none |        | 上报时间               |
| delFlag                    | string            | false | none |        | none                   |
| type                       | string            | false | none |        | 上报类型 1 计划 2 达成 |
| eventNum                   | string            | false | none |        | 会议场次               |
| approveStatus              | string            | false | none |        | 审批状态               |
| periodStartTime            | string            | false | none |        | 上报周期开始时间       |
| approveTime                | string(date-time) | false | none |        | 审批通过时间           |
| approveEmpCode             | string            | false | none |        | 审批通过时间           |
| productName                | string            | false | none |        | 产品名称               |
| productCode                | string            | false | none |        | 产品编码               |
| insName                    | string            | false | none |        | 机构名称               |
| insMdmCode                 | string            | false | none |        | 机构主数据编码         |
| deptName                   | string            | false | none |        | 部门名称               |
| customerName               | string            | false | none |        | 客户名称               |

<h2 id="tocS_RTableDataInfoCrmReportVo">RTableDataInfoCrmReportVo</h2>

<a id="schemartabledatainfocrmreportvo"></a>
<a id="schema_RTableDataInfoCrmReportVo"></a>
<a id="tocSrtabledatainfocrmreportvo"></a>
<a id="tocsrtabledatainfocrmreportvo"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "total": 0,
    "rows": [
      {
        "createBy": 0,
        "createTime": "2019-08-24T14:15:22Z",
        "updateBy": 0,
        "updateTime": "2019-08-24T14:15:22Z",
        "params": {
          "property1": {},
          "property2": {}
        },
        "tenantId": "string",
        "id": 0,
        "insCode": "string",
        "customerCode": "string",
        "period": "string",
        "num": "string",
        "empCode": "string",
        "deptCode": "string",
        "postCode": "string",
        "reportTime": "2019-08-24T14:15:22Z",
        "delFlag": "string",
        "type": "string",
        "eventNum": "string",
        "approveStatus": "string",
        "periodStartTime": "string",
        "approveTime": "2019-08-24T14:15:22Z",
        "approveEmpCode": "string",
        "productName": "string",
        "productCode": "string",
        "insName": "string",
        "insMdmCode": "string",
        "deptName": "string",
        "customerName": "string"
      }
    ],
    "code": 0,
    "msg": "string"
  }
}
```

响应信息主体

### 属性

| 名称 | 类型                                                        | 必选  | 约束 | 中文名 | 说明             |
| ---- | ----------------------------------------------------------- | ----- | ---- | ------ | ---------------- |
| code | integer(int32)                                              | false | none |        | 消息状态码       |
| msg  | string                                                      | false | none |        | 消息内容         |
| data | [TableDataInfoCrmReportVo](#schematabledatainfocrmreportvo) | false | none |        | 表格分页数据对象 |

<h2 id="tocS_TableDataInfoCrmReportVo">TableDataInfoCrmReportVo</h2>

<a id="schematabledatainfocrmreportvo"></a>
<a id="schema_TableDataInfoCrmReportVo"></a>
<a id="tocStabledatainfocrmreportvo"></a>
<a id="tocstabledatainfocrmreportvo"></a>

```json
{
  "total": 0,
  "rows": [
    {
      "createBy": 0,
      "createTime": "2019-08-24T14:15:22Z",
      "updateBy": 0,
      "updateTime": "2019-08-24T14:15:22Z",
      "params": {
        "property1": {},
        "property2": {}
      },
      "tenantId": "string",
      "id": 0,
      "insCode": "string",
      "customerCode": "string",
      "period": "string",
      "num": "string",
      "empCode": "string",
      "deptCode": "string",
      "postCode": "string",
      "reportTime": "2019-08-24T14:15:22Z",
      "delFlag": "string",
      "type": "string",
      "eventNum": "string",
      "approveStatus": "string",
      "periodStartTime": "string",
      "approveTime": "2019-08-24T14:15:22Z",
      "approveEmpCode": "string",
      "productName": "string",
      "productCode": "string",
      "insName": "string",
      "insMdmCode": "string",
      "deptName": "string",
      "customerName": "string"
    }
  ],
  "code": 0,
  "msg": "string"
}
```

表格分页数据对象

### 属性

| 名称  | 类型                                | 必选  | 约束 | 中文名 | 说明       |
| ----- | ----------------------------------- | ----- | ---- | ------ | ---------- |
| total | integer(int64)                      | false | none |        | 总记录数   |
| rows  | [[CrmReportVo](#schemacrmreportvo)] | false | none |        | 列表数据   |
| code  | integer(int32)                      | false | none |        | 消息状态码 |
| msg   | string                              | false | none |        | 消息内容   |
