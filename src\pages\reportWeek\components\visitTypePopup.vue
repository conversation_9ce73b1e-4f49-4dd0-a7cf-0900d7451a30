<template>
  <view>
    <nut-popup
      v-model:visible="state.showPicker"
      position="bottom"
      ref="nutPopupRef"
    >
      <nut-picker
        v-model="state.code"
        :columns="visitTypeList"
        @confirm="onConfirm"
        @cancel="state.showPicker = false"
        :field-names="{
          text: 'dictLabel',
          value: 'dictValue',
        }"
      /> </nut-popup
  ></view>
</template>
<script setup>
import { reactive, ref } from "vue";
import Taro from "@tarojs/taro";
const nutPopupRef = ref(null);
const visitTypeList = Taro.getStorageSync("visitTypeList");
const emit = defineEmits(["visit-type"]);
const state = reactive({
  showPicker: false,
  code: "",
});

const onConfirm = ({ selectedValue, selectedOptions }) => {
  const visitTypeName = selectedOptions.map((val) => val.dictLabel).join(",");
  const visitType = selectedOptions.map((val) => val.dictValue).join(",");
  emit("visit-type", visitTypeName, visitType);
  state.showPicker = false;
};

const open = () => {
  state.showPicker = true;
};

defineExpose({
  open,
});
</script>
<style lang="less" scoped></style>
