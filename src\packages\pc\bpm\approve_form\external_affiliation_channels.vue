<template>
  <div>
    <div class="text-[14px] text-[#2551F2]" v-if="formData.tips">
      注：<br />
      {{ formData.tips }}
    </div>
    <div class="text-[#1D212B] text-lg font-medium font-bold py-[10px]">
      基本信息
    </div>
    <div>
      <div class="sm:pb-0">
        <p
          class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
        >
          医疗机构
        </p>
        <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
          {{ props.formData.insName }}
        </p>
      </div>
      <div class="sm:pb-0">
        <p
          class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
        >
          机构编码
        </p>
        <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
          {{ props.formData.insMdmCode }}
        </p>
      </div>
      <div class="sm:pb-0">
        <p
          class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
        >
          药店距离
        </p>
        <p
          class="mt-1 max-w-lg text-sm/6 text-red-500 max-lg:text-center"
          v-if="props.formData.distanceTooLong"
        >
          {{ props.formData.distanceTooLong }}
        </p>
        <p
          class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center"
          v-else
        >
          {{ props.formData.distance }}
        </p>
      </div>
      <div class="sm:pb-0">
        <p
          class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
        >
          地址
        </p>
        <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
          {{ props.formData.province }}{{ props.formData.city
          }}{{ props.formData.district }}{{ props.formData.address }}
        </p>
      </div>
    </div>
  </div>
  <div class="pt-[16px]">
    <div class="text-[#1D212B] text-lg font-medium font-bold py-[10px]">
      药店信息
    </div>
    <div class="">
      <div class="">
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            药店名称
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.dsName }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            药店编码
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.dsMdmCode }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            关联药店类型
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.insDsType }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            上级药店
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.superiorDsName || "--" }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            上级药店编码
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.superiorDsCode || "--" }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            数据采集方式
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.dsSource }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            药店性质
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.dsNature }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            药店支付类型
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.dsEconomicStr }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            是否HIS对接
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData.hisDocking }}
          </p>
        </div>
        <div class="sm:pb-0">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            科室
          </p>
          <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
            {{ props.formData?.departmentCodeList?.map(dept => dept.departmentName)?.join('、') || '-' }}
          </p>
        </div>
        <div
          class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"
        ></div>
        <div
          v-for="spec in props.formData.specList || props.formData.insDsList"
        >
          <div class="sm:pb-0">
            <p
              class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
            >
              品规名称
            </p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
              {{ spec.value }}
            </p>
          </div>
          <div class="sm:pb-0">
            <p
              class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
            >
              是否商务开发
            </p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
              {{ props.formData.businessContact === "1" ? "是" : "否" }}
            </p>
          </div>
          <div class="sm:pb-0">
            <p
              class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
            >
              开发状态
            </p>
            <p class="mt-1 max-w-lg text-sm/6 text-gray-600 max-lg:text-center">
              {{ props.formData.salesStatus || "--" }}
            </p>
          </div>
        </div>

        <div
          class="mx-[16px] my-[8px] h-[1px] border-[1px] border-dashed border-transparent border-b-[#E5E6EB]"
        ></div>
        <div class="">
          <p
            class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
          >
            照片(营业执照、经营许可证、门头照)
          </p>
          <div style="display: flex">
            <div
              style="margin-right: 20px"
              v-for="(item, index) in state.fileList"
              :key="index"
            >
              <el-image
                :initial-index="index"
                :preview-src-list="state.fileList"
                style="width: 300px; height: 200px"
                :src="item"
                fit="fill"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div
    class="mt-1 mb-[10px] text-lg font-medium tracking-tight text-gray-950 max-lg:text-center"
  >
    关联药店
  </div>
  <div class="bg-white px-[16px] pb-[16px]">
    <div class="text-[#4E595E] text-[14px] pb-[12px]">
      已关联({{ props.formData.insDsList?.length || "0" }})
    </div>
    <div
      v-for="item in props.formData.insDsList || []"
      :key="item.dsMdmCode"
      class="px-[16px] py-[8px] bg-[#F2F6FF] rounded-[4px] mb-[12px]"
      style="border: 1px solid #92a8f8"
    >
      <div class="text-[#2551F2] text-[16px] pb-[4px]">{{ item.dsName }}</div>
      <div class="text-[#869199] text-[12px]">
        药店编码：{{ item.dsMdmCode }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { dowTemplte } from "@/bpm/src/api/flow/index";
const props = defineProps({
  formData: {
    type: Object,
    default: () => {
      return {};
    },
  },
  formType: {
    type: Object,
    default: "",
  },
});
const state = reactive({
  info: {},
  specList: [],
  fileList: [],
});
onMounted(() => {
  if (props.formData.fileList) {
    JSON.parse(props.formData.fileList).forEach(async (el) => {
      const res = await dowTemplte(el.ossId);
      state.fileList.push(res.data.rows?.[0]?.url);
    });
  }
});
</script>
<style lang="scss" scoped></style>
