import httpRequest from "@/servers/http";

/**
 * 新增或更新上报
 * @param {Object} params - CrmReport对象
 * @param {number} params.id - ID（更新时必填）
 * @param {string} params.insCode - 机构编码
 * @param {string} params.customerCode - 客户编码
 * @param {string} params.period - 上报周期
 * @param {string} params.num - 诊疗体验数量
 * @param {string} params.empCode - 上报人工号
 * @param {string} params.deptCode - 部门编码
 * @param {string} params.postCode - 岗位编码
 * @param {string} params.type - 上报类型 1计划 2达成
 * @param {string} params.eventNum - 会议场次
 * @param {string} params.approveStatus - 审批状态
 * @param {string} params.periodStartTime - 上报周期开始时间
 * @param {string} params.productName - 产品名称
 * @param {string} params.productCode - 产品编码
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport
 */
export function addOrUpdateReport(params) {
  return httpRequest.post("/ade/crmReport", params);
}

/**
 * 获取上报列表
 * @param {Object} params - ReportQueryVo对象
 * @param {string} params.period - 上报周期
 * @param {Array<number>} params.deptIdList - 下级部门ID列表
 * @param {Array<string>} params.deptCodeList - 下级部门编码列表
 * @param {string} params.type - 上报类型 1计划 2达成
 * @param {string} params.approveStatus - 审批状态
 * @param {boolean} params.querySub - 查询下属
 * @param {Object} params.query - 分页查询参数
 * @param {number} params.query.pageSize - 分页大小
 * @param {number} params.query.pageNum - 当前页数
 * @param {string} params.query.orderByColumn - 排序列
 * @param {string} params.query.isAsc - 排序方向desc或者asc
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport/list
 */
export function getReportList(params) {
  return httpRequest.post("/ade/crmReport/list", params);
}

/**
 * 批量审批
 * @param {Array<number>} ids - 需要审批的ID数组
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport/batchApprove
 */
export function batchApproveReport(ids) {
  return httpRequest.post("/ade/crmReport/batchApprove", ids);
}

/**
 * 审批报表
 * @param {Object} params - CrmReport对象
 * @param {number} params.id - ID
 * @param {string} params.approveStatus - 审批状态
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport/approve
 */
export function approveReport(params) {
  return httpRequest.post("/ade/crmReport/approve", params);
}

/**
 * 获取上报详情
 * @param {Object} params - 查询参数
 * @param {string} params.empCode - 员工编码（必填）
 * @param {string} params.insCode - 机构编码（必填）
 * @param {string} params.customerCode - 客户编码（必填）
 * @param {string} params.type - 上报类型（必填）
 * @returns {Promise}
 * 接口路径: GET /ade/crmReport/detail
 */
export function getReportDetail(params) {
  return httpRequest.get("/ade/crmReport/detail", params);
}

/**
 * 删除上报
 * @param {Object} params - CrmReport对象
 * @param {number} params.id - ID
 * @returns {Promise}
 * 接口路径: DELETE /ade/crmReport/delete
 */
export function deleteReport(params) {
  return httpRequest.delete("/ade/crmReport/delete", params);
}

/**
 * 获取拜访统计数据
 * @param {Object} params - ReportQueryVo对象
 * @param {string} params.period - 上报周期
 * @param {string} params.date - 日期
 * @param {Array<number>} params.deptIdList - 下级部门ID列表
 * @param {Array<string>} params.deptCodeList - 下级部门编码列表
 * @param {string} params.type - 上报类型 1计划 2达成
 * @param {string} params.approveStatus - 审批状态
 * @param {boolean} params.querySub - 查询下属
 * @param {Object} params.query - 分页查询参数
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport/visit/statistics
 */
export function getVisitStatistics(params) {
  return httpRequest.post("/ade/crmReport/visit/statistics", params);
}

/**
 * 获取拜访达成列表
 * @param {Object} params - ReportQueryVo对象
 * @param {string} params.period - 上报周期
 * @param {string} params.date - 日期
 * @param {Array<number>} params.deptIdList - 下级部门ID列表
 * @param {Array<string>} params.deptCodeList - 下级部门编码列表
 * @param {string} params.type - 上报类型 1计划 2达成
 * @param {string} params.approveStatus - 审批状态
 * @param {boolean} params.querySub - 查询下属
 * @param {Object} params.query - 分页查询参数
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport/visit/list
 */
export function getVisitList(params) {
  return httpRequest.post("/ade/crmReport/visit/list", params);
}

/**
 * 拜访批量审批
 * @param {Array<number>} ids - 需要审批的ID数组
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport/visit/batchApprove
 */
export function batchApproveVisit(ids) {
  return httpRequest.post("/ade/crmReport/visit/batchApprove", ids);
}

/**
 * 获取统计数据
 * @param {Object} params - ReportQueryVo对象
 * @param {string} params.period - 上报周期
 * @param {string} params.date - 日期
 * @param {Array<number>} params.deptIdList - 下级部门ID列表
 * @param {Array<string>} params.deptCodeList - 下级部门编码列表
 * @param {string} params.type - 上报类型 1计划 2达成
 * @param {string} params.approveStatus - 审批状态
 * @param {boolean} params.querySub - 查询下属
 * @param {Object} params.query - 分页查询参数
 * @returns {Promise}
 * 接口路径: POST /ade/crmReport/statistics
 */
export function getStatistics(params) {
  return httpRequest.post("/ade/crmReport/statistics", params);
}
