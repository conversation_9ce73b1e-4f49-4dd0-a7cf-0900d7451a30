<template>
  <view class="institutionalVisits">
    <nav-bar @click-back="clickBack" navBarStyle="padding-top:10px">
      <template #left> <div></div></template>
      <template #titleContent>
        <nav-title
          :activeIndex="state.activeIndex"
          leftText=""
          rightText=""
          @setActiveIndex="setActiveIndex"
        ></nav-title>
      </template>
    </nav-bar>

    <VisitCalendar2 v-if="active === 2"
      ref="visitCalendarRef"
      @chage-time="chageTime"
      :visitTime="state.visitTime"
      :visitEndTime="state.visitEndTime"
      :type="active"
    />
    <VisitCalendar v-else
      ref="visitCalendarRef"
      @chage-time="chageTime"
      :visitTime="state.visitTime"
      :visitEndTime="state.visitEndTime"
      :type="active"
    />
    <!-- 日历 -->
    <!-- <calendar
      :date-list="state.dateList"
      @change-date="onChangeDate"
    ></calendar> -->

    <!-- 统计字段 -->
    <view class="statistics-header">
      <view v-if="active === 2" class="stats-grid">
        <!-- 拜访统计 -->
        <view class="stats-item">
          <view class="stats-label">目标客户:</view>
          <view class="stats-value">{{ state.statistics.targetCustomerCount }}</view>
        </view>
        <view class="stats-item">
          <view class="stats-label">已拜访客户:</view>
          <view class="stats-value">{{ state.statistics.visitedCustomer }}</view>
        </view>
        <view class="stats-item">
          <view class="stats-label">诊疗体验数量:</view>
          <view class="stats-value">{{ state.statistics.numCount }}</view>
        </view>
      </view>
      <view v-else-if="active === 1" class="stats-grid">
        <!-- 活动统计 -->
        <view class="stats-item">
          <view class="stats-label">目标客户:</view>
          <view class="stats-value">{{ state.statistics.targetCustomerCount }}</view>
        </view>
        <view class="stats-item">
          <view class="stats-label"></view>
          <view class="stats-value"></view>
        </view>
        <view class="stats-item">
          <view class="stats-label">已提交:</view>
          <view class="stats-value">{{ state.statistics.submittedCount }}</view>
        </view>
        <view class="stats-item">
          <view class="stats-label">会议场次:</view>
          <view class="stats-value">{{ state.statistics.submittedEventCount }}</view>
        </view>
        <view class="stats-item">
          <view class="stats-label">已确认:</view>
          <view class="stats-value">{{ state.statistics.confirmedCount }}</view>
        </view>
        <view class="stats-item">
          <view class="stats-label">会议场次:</view>
          <view class="stats-value">{{ state.statistics.confirmedEventCount }}</view>
        </view>
      </view>
      <view v-else-if="active === 0" class="stats-grid">
        <!-- 计划统计 -->
        <view class="stats-item-3">
          <view class="stats-label">目标客户:</view>
          <view class="stats-value">{{ state.statistics.targetCustomerCount }}</view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label"></view>
          <view class="stats-value"></view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label"></view>
          <view class="stats-value"></view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label">已提交:</view>
          <view class="stats-value">{{ state.statistics.submittedCount }}</view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label">诊疗体验总数:</view>
          <view class="stats-value">{{ state.statistics.submittedNumCount }}</view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label">会议场次:</view>
          <view class="stats-value">{{ state.statistics.submittedEventCount }}</view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label">已确认:</view>
          <view class="stats-value">{{ state.statistics.confirmedCount }}</view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label">诊疗体验总数:</view>
          <view class="stats-value">{{ state.statistics.confirmedNumCount }}</view>
        </view>
        <view class="stats-item-3">
          <view class="stats-label">会议场次:</view>
          <view class="stats-value">{{ state.statistics.confirmedEventCount }}</view>
        </view>
      </view>
    </view>

    <!-- 头部筛选  -->
    <TopSearch
      ref="topSearchRef"
      :list="state.topSearchList"
      @activeItem="activeItem"
      :selectFlag="state.selectFlag"
      @sub-user-select="subUsereSelect"
      :subEmpCode="state.subParams.subEmpCode"
      :scrollFlag="state.scrollFlag"
    />

    <!-- 批量审批功能 - 在下属nav显示 -->
    <view v-if="state.activeIndex === 1" class="batch-toggle" @tap="toggleBatchMode">
      <img :src="batchIcon" class="batch-icon" v-if="!state.batchMode" />
      <img :src="batchedIcon" class="batch-icon active" v-else />
      <view class="batch-text">批量审批(最多可一次选择10条)</view>
    </view>

    <view style="background: #edeff5" :style="{
      paddingBottom: state.batchMode ? '140px' : '60px'
    }">
      <plan-list v-if="active === 2"
        :institutionList="state.institutionList"
        @scrolltolower="scrolltolower"
        :activeIndex="state.activeIndex"
        :total="state.total"
        :batchMode="state.batchMode"
        :selectedItems="state.selectedItems"
        @toggle-select="toggleSelect"
        :canSelect="canSelect"
        :type="'-1'"  
      ></plan-list>
      <plan-list
        v-else
        :institutionList="state.institutionList"
        @scrolltolower="scrolltolower"
        :activeIndex="state.activeIndex"
        :total="state.total"
        :batchMode="state.batchMode"
        :selectedItems="state.selectedItems"
        @toggle-select="toggleSelect"
        :canSelect="canSelect"
        :type="active === 0 ? '1' : '2'"
      ></plan-list>
    </view>

    <custom-popup
      :show-popup-visible="state.showFooterPopupVisible"
      :closeable="false"
      @click-overlay="state.showFooterPopupVisible = false"
    >
      <institution-type @clickTitle="state.showFooterPopupVisible = false" />
    </custom-popup>

    <view class="footerFiexd">
      <view class="add-button" @click="dragClick">
        新增
      </view>
    </view>

    <!-- 批量操作底部栏 -->
    <view v-if="state.batchMode" class="batch-bottom-bar">
      <view class="batch-left">
        <img :src="state.isAllSelected ? radioIcon2 : radioIcon1" class="select-all-icon" @click="toggleSelectAll" />
        <view class="select-all-text" @tap="toggleSelectAll">全选</view>
        <view class="select-tip">（最多10条）</view>
      </view>
      <view class="batch-right">
        <view class="cancel-btn" @tap="cancelBatch">取消批量</view>
        <view class="approve-btn" :class="{'disabled': !state.selectedItems.length}" @tap="handleBatchApprove">
          同意
        </view>
      </view>
    </view>
  </view>

  <nut-tabbar v-model="active" unactive-color="#7d7e80" active-color="#1989fa" v-if="!state.batchMode" class="fixed-tabbar">
    <nut-tabbar-item v-for="(item, index) in List" :key="index" :tab-title="item.title" :icon="item.icon">
    </nut-tabbar-item>
  </nut-tabbar>
</template>
<script setup>
import Taro, { useDidShow, useRouter } from "@tarojs/taro";
import { Home, Category, Find, Cart, My } from '@nutui/icons-vue-taro';
import NavBar from "../../pages/components/navBar/index.vue";
import { onMounted, reactive, watch, h, ref } from "vue";
import navTitle from "../../pages/components/navTitle/navTitle.vue";
import institutionList from "../../pages/reportWeek/components/institutionList.vue";
import planList from "./components/planList.vue";
import { institutionalVisitsList } from "../../api/institutionalVisitsApi.js";
import { getReportList, batchApproveReport, getVisitList, getStatistics, getVisitStatistics, batchApproveVisit } from "./report/api.js";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import CustomPopup from "../../pages/components/customPopup/index";
import InstitutionType from "../../pages/reportWeek/components/institutionType.vue";
import TopSearch from "./components/topSearch.vue";
import VisitCalendar from "./components/visitCalendar.vue";
import batchIcon from "./batch.png";
import batchedIcon from "./batched.png";
import radioIcon1 from "./radio-icon-unchecked.png";
import radioIcon2 from "./radio_checked_icon.png";
import VisitCalendar2 from "./components/visitCalendar2.vue";

const initHrInfo = Taro.getStorageSync("initHrInfo");
const router = useRouter();
const active = ref(0);
const topSearchRef = ref(null);
const visitCalendarRef = ref(null);
const state = reactive({
  activeIndex: 0, // nav-title 控制的索引，用于 querySub 参数
  visitTime: dayjs().format("YYYY-MM-DD"),
  pageNum: 1,
  pageSize: 10,
  institutionList: [],
  showFooterPopupVisible: false,
  showPopupVisible: false,
  visitEndTime: dayjs().format("YYYY-MM-DD"),
  dateList: [
    { color: "#b3b3b3", date: [] },
    { color: "#fff", type: "dot", date: [] },
  ],
  status: [],
  selectFlag: false,
  // 下属
  subParams: {
    subEmpCode: initHrInfo.subUserCodeList,
    subVisit: "1",
  },
  scrollFlag: false,
  total: 0,
  // 批量审批相关状态
  batchMode: false, // 是否处于批量模式
  selectedItems: [], // 已选择的项目ID
  isAllSelected: false,
  // 统计数据
  statistics: {
    targetCustomerCount: 0, // 目标客户
    visitedCustomer: 0, // 已拜访客户
    numCount: 0, // 诊疗体验数量
    submittedCount: 0, // 已提交
    submittedNumCount: 0, // 已提交诊疗体验总数
    submittedEventCount: 0, // 已提交会议场次
    confirmedCount: 0, // 已确认
    confirmedNumCount: 0, // 已确认诊疗体验总数
    confirmedEventCount: 0 // 已确认会议场次
  }
});

const List = [
  {
    title: '计划',
    icon: h(Home),
    name: '计划',
    type: '1'
  },
  {
    title: '活动达成',
    icon: h(Category),
    name: '活动达成',
    type: '2'
  },
  {
    title: '拜访达成',
    icon: h(Category),
    name: '拜访达成'
  },
]
const clickBack = () => {
  Taro.reLaunch({
    url: "/pages/index/index",
  });
};
// nav-title 切换处理（控制 querySub 参数）
const setActiveIndex = (v) => {
  console.log('setActiveIndex called with:', v, 'current activeIndex:', state.activeIndex);

  if (v === state.activeIndex) return;

  // 清空及初始化查询状态
  state.pageNum = 1;
  state.institutionList = [];
  state.total = 0;
  state.status = [];
  state.scrollFlag = false;

  // 清空批量审批状态
  state.batchMode = false;
  state.selectedItems = [];
  state.isAllSelected = false;

  // 重置下属筛选
  state.subParams.subEmpCode = initHrInfo.subUserCodeList;

  // 设置 selectFlag
  if (v === 1) {
    state.selectFlag = true;
  } else {
    state.selectFlag = false;
  }

  state.activeIndex = v;

  // 只有在页面已经初始化后才调用接口
  if (isPageInitialized) {
    console.log('setActiveIndex: 调用getinList');
    getinList();
  } else {
    console.log('setActiveIndex: 跳过getinList调用（页面未初始化）');
  }
};

// 下属筛选出来的代表
const subUsereSelect = (list) => {
  if (!list.length) {
    state.subParams.subEmpCode = initHrInfo.subUserCodeList;
  } else {
    const arr = list.map((item) => item.empCode);
    state.subParams.subEmpCode = arr;
  }
  state.total = 0;
  state.pageNum = 1;
  state.institutionList = [];
  getinList();
};
// 添加标志位来控制接口调用
let isTabbarSwitching = false;
let isPageInitialized = false;

const chageTime = (start, end) => {
  console.log('chageTime called:', start, end, 'isTabbarSwitching:', isTabbarSwitching);

  state.visitTime = start;
  state.visitEndTime = end;

  // 如果是tabbar切换触发的，不重复调用接口
  if (!isTabbarSwitching) {
    console.log('chageTime: 调用getinList');
    state.pageNum = 1;
    state.total = 0;
    state.institutionList = [];
    getinList();

    // 标记页面已经初始化（首次数据加载完成）
    if (!isPageInitialized) {
      isPageInitialized = true;
      console.log('页面标记为已初始化');
    }
  } else {
    console.log('chageTime: 跳过getinList调用（tabbar切换中）');
  }
};

// 底部弹出层
const dragClick = () => {
  // if (state.activeIndex === 0) {
  //   state.showFooterPopupVisible = !state.showFooterPopupVisible;
  // } else {
  //   state.showPopupVisible = !state.showPopupVisible;
  // }
  if(active.value === 2) {
    Taro.navigateTo({
      url: `pages/institutionalVisits048/sign/signIn`,
    });
  }else {
    Taro.navigateTo({
      url: `pages/reportWeek/report/edit?type=${active.value === 0 ? '1' : '2'}`,
    });
  }
};

// 获取统计数据
const getStatisticsData = async () => {
  try {
    const params = {
      period: `${dayjs(state.visitTime).format('YYYY-MM-DD')}~${dayjs(state.visitEndTime).format('YYYY-MM-DD')}`, // 时间段格式
      querySub: state.activeIndex === 1, // 是否查询下属
      deptIdList: initHrInfo.deptIdList,
    };

    // 下属参数
    // if (state.activeIndex === 1 && state.subParams.subEmpCode.length) {
    //   params.deptCodeList = state.subParams.subEmpCode;
    // }

    let res;
    if (active.value === 2) {
      // 拜访达成统计
      res = await getVisitStatistics(params);
    } else {
      // 计划统计
      params.type = active.value === 0 ? '1' : '2';
      res = await getStatistics(params);
    }

    if (res?.code === 200 && res.data) {
      // 根据接口返回的数据结构更新统计信息
      state.statistics = {
        targetCustomerCount: res.data.targetCustomerCount || 0,
        visitedCustomer: res.data.visitedCustomer || 0,
        numCount: res.data.numCount || 0,
        submittedCount: res.data.submittedCount || 0,
        submittedNumCount: res.data.submittedNumCount || 0,
        submittedEventCount: res.data.submittedEventCount || 0,
        confirmedCount: res.data.confirmedCount || 0,
        confirmedNumCount: res.data.confirmedNumCount || 0,
        confirmedEventCount: res.data.confirmedEventCount || 0
      };
    }
  } catch (err) {
    console.error('获取统计数据失败:', err);
  }
};

const getinList = async () => {
  console.log('getinList called');
  state.scrollFlag = true;
  // 上报相关数据，使用新的API
  let reportType = '';
  if (active.value === 0) {
    reportType = '1'; // 计划
  } else if (active.value === 1) {
    reportType = '2'; // 达成
  }

  let params = {
    type: reportType,
    period: `${dayjs(state.visitTime).format('YYYY-MM-DD')}~${dayjs(state.visitEndTime).format('YYYY-MM-DD')}`, // 时间段格式：开始时间~结束时间
    approveStatus: state.status.join(",") || undefined, // 审批状态筛选
    querySub: state.activeIndex === 1, // 是否查询下属 - 使用 nav-title 的 activeIndex
    deptIdList: initHrInfo.deptIdList,
    query: {
      pageSize: state.pageSize,
      pageNum: state.pageNum,
      // orderByColumn: 'createTime',
      // isAsc: 'desc'
    }
  };

  // 下属参数 - 使用 nav-title 的 activeIndex
  // if (state.activeIndex === 1 && state.subParams.subEmpCode.length) {
  //   // 这里可能需要根据实际API调整参数结构
  //   params.deptCodeList = state.subParams.subEmpCode;
  // }

  try {
    Taro.showLoading({
      title: "加载中",
      icon: "loading",
    });
    const api = active.value === 2 ? getVisitList : getReportList;
    const res = await api(params);
    Taro.hideLoading();
    if (!state.institutionList.length) {
      state.institutionList = res.data?.rows || [];
    } else {
      state.institutionList = [...state.institutionList, ...(res.data?.rows || [])];
    }
    state.total = res.data?.total || 0;

    // 获取统计数据
    if (state.pageNum === 1) {
      await getStatisticsData();
    }
  } catch (err) {
    Taro.hideLoading();
    console.error('获取列表数据失败:', err);
    Taro.showToast({
      title: '加载失败',
      icon: 'none'
    });
  } finally {
    state.scrollFlag = false;
  }
};

// 滚动加载
const scrolltolower = () => {
  if (!state.scrollFlag && state.institutionList.length < state.total) {
    state.pageNum++;
    getinList();
  }
};
const activeItem = (keys) => {
  // 筛选出来的状态
  if (!state.scrollFlag) {
    state.total = 0;
    state.pageNum = 1;
    state.institutionList = [];
    state.status = keys;
    getinList();
  }
};

// 批量审批相关方法
const toggleBatchMode = () => {
  state.batchMode = !state.batchMode;
  // 打开批量模式时，默认全部不选
  state.selectedItems = [];
  state.isAllSelected = false;
};

// 获取项目的唯一标识符
const getItemId = (item) => {
  // 根据 tabbar 的 active 值决定使用哪个字段作为ID
  if (active.value === 0) {
    return item.id; // 拜访数据使用instanceId
  } else {
    return item.id; // 上报数据使用id
  }
};

// 只有未满10条或已选中的才可点，且已确认的数据不可选择
const canSelect = (item) => {
  // 已确认 未提交的数据不可选择
  if (item.approveStatus !== '1') {
    return false;
  }

  const itemId = getItemId(item);
  return state.selectedItems.length < 10 || state.selectedItems.includes(itemId);
};

// 勾选/取消勾选
const toggleSelect = (item) => {
  // 已确认的数据不允许选择
  if (item.approveStatus === '2') {
    Taro.showToast({
      title: '已确认的数据无需审批',
      icon: 'none'
    });
    return;
  }

  const itemId = getItemId(item);
  const idx = state.selectedItems.indexOf(itemId);
  if (idx === -1) {
    if (state.selectedItems.length < 10) {
      state.selectedItems.push(itemId);
    }
  } else {
    state.selectedItems.splice(idx, 1);
  }
};

// 只有点击全选按钮才全选/取消全选
const toggleSelectAll = () => {
  // 过滤掉已确认的数据，只选择可审批的数据
  const selectableItems = state.institutionList.slice(0, 10).filter(item => item.approveStatus !== '2');
  const ids = selectableItems.map(item => getItemId(item));

  if (state.isAllSelected) {
    // 取消全选：只移除这些可选择的条目
    state.selectedItems = state.selectedItems.filter(id => !ids.includes(id));
  } else {
    // 全选：清除现有选择，选择可审批的数据
    state.selectedItems = ids;

    // 如果有已确认的数据被跳过，给出提示
    const skippedCount = state.institutionList.slice(0, 10).length - selectableItems.length;
    if (skippedCount > 0) {
      Taro.showToast({
        title: `已跳过${skippedCount}条已确认数据`,
        icon: 'none'
      });
    }
  }
  state.isAllSelected = !state.isAllSelected;
};

const cancelBatch = () => {
  state.batchMode = false;
  state.selectedItems = [];
  state.isAllSelected = false;
};

const handleBatchApprove = async () => {
  if (state.selectedItems.length === 0) {
    Taro.showToast({
      title: '请至少选择一条申请单',
      icon: 'none'
    });
    return;
  }

  // 检查选中的数据中有多少已确认的
  const confirmedCount = state.selectedItems.filter(id => {
    const item = state.institutionList.find(inst => getItemId(inst) === id);
    return item && item.approveStatus === '2';
  }).length;

  const pendingCount = state.selectedItems.length - confirmedCount;

  let content = `确定要批量审批选中的${state.selectedItems.length}条记录吗？`;
  if (confirmedCount > 0) {
    content += `\n（其中${confirmedCount}条已确认数据将被跳过）`;
  }

  // 确认批量审批
  const confirmResult = await Taro.showModal({
    title: '批量审批确认',
    content: content,
    confirmText: '确定',
    cancelText: '取消'
  });

  if (!confirmResult.confirm) {
    return;
  }

  Taro.showLoading({
    title: '正在批量审批...',
    mask: true
  });
  try {
    // 获取选中的项目ID，并过滤掉已确认的数据
    const selectedIds = state.selectedItems.filter(id => {
      if (id === undefined || id === null) return false;

      // 查找对应的数据项，检查是否已确认
      const item = state.institutionList.find(inst => getItemId(inst) === id);
      if (item && item.approveStatus === '2') {
        return false; // 已确认的数据不参与批量审批
      }
      return true;
    });

    // 如果过滤后没有可审批的数据
    if (selectedIds.length === 0) {
      Taro.hideLoading();
      Taro.showToast({
        title: '所选数据均已确认，无需审批',
        icon: 'none'
      });
      return;
    }

    let res;
    // 根据当前tab调用不同的批量审批接口
    if (active.value === 2) {
      // 拜访达成数据，使用拜访批量审批接口
      res = await batchApproveVisit(selectedIds);
    } else {
      // 计划数据，使用上报批量审批接口
      res = await batchApproveReport(selectedIds);
    }

    Taro.hideLoading();

    if (res?.code === 200) {
      const originalCount = state.selectedItems.length;
      const actualCount = selectedIds.length;
      let message = `批量审批成功，共审批${actualCount}条记录`;

      if (originalCount > actualCount) {
        message += `（已跳过${originalCount - actualCount}条已确认数据）`;
      }

      Taro.showToast({
        title: message,
        icon: 'success'
      });
      cancelBatch();
      state.institutionList = [];
      state.pageNum = 1;
      // 重新加载数据和统计信息
      await getinList();
    } else {
      Taro.showToast({
        title: res?.msg || '批量审批失败',
        icon: 'none'
      });
    }
  } catch (e) {
    Taro.hideLoading();
    Taro.showToast({
      title: '批量审批失败',
      icon: 'none'
    });
    console.error('批量审批错误:', e);
  }
};

// 监听 tabbar 切换
watch(
  () => active.value,
  async (newActive, oldActive) => {
    if (newActive !== oldActive) {
      // 设置标志位，防止chageTime重复调用接口
      isTabbarSwitching = true;

      // 清空及初始化所有查询状态
      state.pageNum = 1;
      state.institutionList = [];
      state.total = 0;
      state.status = []; // 清空筛选状态
      state.scrollFlag = false;

      // 重置nav-title到第一个（activeIndex = 0）- 直接设置，避免触发setActiveIndex
      state.activeIndex = 0;

      // 重置selectFlag
      state.selectFlag = false;

      // 清空TopSearch筛选状态（静默重置，不触发事件）
      if (topSearchRef.value) {
        topSearchRef.value.resetFiltersSilent();
      }

      // 重置日期选择器到默认值 - 等待重置完成
      if (visitCalendarRef.value) {
        console.log('开始重置日期选择器...');
        await visitCalendarRef.value.resetToDefault();
        console.log('日期选择器重置完成，当前日期:', state.visitTime, '~', state.visitEndTime);
      }

      // 确保页面显示的日期立即更新
      // 这里可以添加一个小延迟确保DOM更新完成
      await new Promise(resolve => setTimeout(resolve, 50));

      // 清空批量审批状态
      state.batchMode = false;
      state.selectedItems = [];
      state.isAllSelected = false;

      // 重置下属筛选
      state.subParams.subEmpCode = initHrInfo.subUserCodeList;

      // 重置标志位
      isTabbarSwitching = false;

      // 等待日期重置完成后再加载数据
      getinList();
    }
  }
);

watch(
  () => {
    state.status;
  },
  () => {
    state.institutionList = [];
  }
);
useDidShow(() => {
  console.log('useDidShow called, isPageInitialized:', isPageInitialized);
    console.log('useDidShow: 页面已初始化，重新加载数据');
    state.pageNum = 1;
    state.total = 0;
    state.institutionList = [];
    // getinList();


  // 只有在页面已经初始化后才重新加载数据
  // 避免与页面初次加载时的接口调用重复
  // if (isPageInitialized) {
  //   console.log('useDidShow: 页面已初始化，重新加载数据');
  //   state.pageNum = 1;
  //   state.total = 0;
  //   state.institutionList = [];
  //   getinList();
  // } else {
  //   console.log('useDidShow: 页面首次显示，跳过数据加载');
  //   // 页面首次显示时不加载数据，等待visitCalendar组件初始化触发
  // }
});
</script>
<style lang="scss">
.institutionalVisits {
  font-size: 16px;
  padding-top: 60px;
  .footerFiexd {
    margin-left: 80%;
    position: fixed;
    bottom: 90px;
    right: 20px;
  }
  .add-button {
    width: 50px;
    padding: 6px 12px;
    border-radius: 25px;
    background-color: #E8F0FF;
    color: #2551F2;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0px 4px 8px 0px rgba(37, 81, 242, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      box-shadow: 0px 2px 4px 0px rgba(37, 81, 242, 0.1);
    }
  }
  .nut-navbar__title {
    margin-left: 66px;
  }

  // 批量审批切换按钮样式
  .batch-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 12px;
    margin-bottom: 10px;
    cursor: pointer;

    .batch-icon {
      width: 14px;
      height: 20px;
      opacity: 0.5;

      &.active {
        opacity: 1;
      }
    }

    .batch-text {
      font-size: 12px;
      color: #999;
    }
  }

  // 批量操作底部栏样式
  .batch-bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top: 1px solid #EAEBEC;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 50;
    min-height: 60px;

    .batch-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .select-all-icon {
        width: 20px;
        height: 20px;
      }

      .select-all-text {
        font-size: 16px;
        color: #2551F2;
        font-weight: bold;
      }

      .select-tip {
        font-size: 13px;
        color: #869199;
        margin-left: 2px;
      }
    }

    .batch-right {
      display: flex;
      align-items: center;
      gap: 8px;

      .cancel-btn {
        width: 80px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-size: 15px;
        color: #1D212B;
        background-color: #F3F4F5;
        border: 1px solid #EAEBEC;
        border-radius: 4px;
        margin-right: 8px;
      }

      .approve-btn {
        width: 80px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-size: 15px;
        color: #fff;
        background-color: #2551F2;
        border: 1px solid #2551F2;
        border-radius: 4px;

        &.disabled {
          opacity: 0.5;
        }
      }
    }
  }
}

/* 统计数据样式 */
.statistics-container {
  background: #fff;
  padding: 16px;
  margin-bottom: 8px;
}

.statistics-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
}

.stat-label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  margin-right: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #2551f2;
}

/* 头部筛选上方的统计字段样式 */
.statistics-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
}

.stats-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px;
}

.stats-item {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
  min-width: 120px;
}

.stats-item-3 {
  display: flex;
  align-items: center;
  min-width: 0;
  flex: 1;
  min-width: 100px;
}


.stats-label {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
  margin-right: 4px;
}

.stats-value {
  font-size: 15px;
  font-weight: 600;
  color: #2551f2;
}

/* 固定底部tabbar样式 */
.fixed-tabbar {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  background: #fff !important;
  border-top: 1px solid #e9ecef !important;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 为页面内容添加底部间距，避免被tabbar遮挡 */
.institutionalVisits {
  padding-bottom: 60px; /* tabbar高度 + 安全距离 */
}
</style>
